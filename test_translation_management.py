#!/usr/bin/env python3
"""
اختبار نظام إدارة الترجمة والبرومبتات المنفصلة
"""

import os
import sys
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_translation_prompts_system():
    """اختبار نظام البرومبتات المنفصلة"""
    print("🔍 اختبار نظام البرومبتات المنفصلة...")
    
    try:
        from keyboards.builder import (
            get_translation_text_prompt, get_translation_image_prompt,
            set_translation_text_prompt, set_translation_image_prompt,
            create_translation_button
        )
        
        # إنشاء زر ترجمة للاختبار
        test_button_id = 9999
        create_translation_button(test_button_id, 'to_arabic')
        
        # اختبار تحديد برومبت النص
        text_prompt = "هذا برومبت مخصص لترجمة النصوص العادية إلى العربية"
        set_translation_text_prompt(test_button_id, text_prompt)
        
        # اختبار تحديد برومبت الصور
        image_prompt = "هذا برومبت مخصص لترجمة النصوص المستخرجة من الصور إلى العربية"
        set_translation_image_prompt(test_button_id, image_prompt)
        
        # اختبار جلب البرومبتات
        retrieved_text_prompt = get_translation_text_prompt(test_button_id)
        retrieved_image_prompt = get_translation_image_prompt(test_button_id)
        
        if retrieved_text_prompt == text_prompt:
            print("✅ برومبت ترجمة النص تم حفظه وجلبه بنجاح")
        else:
            print(f"❌ خطأ في برومبت النص: متوقع '{text_prompt}', تم جلب '{retrieved_text_prompt}'")
            return False
        
        if retrieved_image_prompt == image_prompt:
            print("✅ برومبت ترجمة الصور تم حفظه وجلبه بنجاح")
        else:
            print(f"❌ خطأ في برومبت الصور: متوقع '{image_prompt}', تم جلب '{retrieved_image_prompt}'")
            return False
        
        # تنظيف بيانات الاختبار
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM translation_settings WHERE button_id = ?', (test_button_id,))
        cursor.execute('DELETE FROM translation_global_settings WHERE key LIKE ?', (f'%_{test_button_id}',))
        conn.commit()
        conn.close()
        
        print("✅ نظام البرومبتات المنفصلة يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البرومبتات: {str(e)}")
        return False

def test_translation_management_callbacks():
    """اختبار callback data لأزرار الإدارة"""
    print("\n🔍 اختبار callback data لأزرار الإدارة...")
    
    try:
        # قائمة أزرار الإدارة المتوقعة
        expected_callbacks = [
            "edit_translation_api:123",
            "edit_translation_model:123", 
            "edit_translation_prompt:123",
            "edit_translation_start:123",
            "change_translation_lang:123",
            "edit_translation_cost:123"
        ]
        
        for callback in expected_callbacks:
            # التحقق من صحة تنسيق callback
            if ":" in callback and callback.split(":")[1].isdigit():
                action = callback.split(":")[0]
                button_id = int(callback.split(":")[1])
                print(f"✅ {action} - صحيح (button_id: {button_id})")
            else:
                print(f"❌ {callback} - تنسيق خاطئ")
                return False
        
        print("✅ جميع callback data صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار callback data: {str(e)}")
        return False

def test_translation_states():
    """اختبار حالات التعديل الجديدة"""
    print("\n🔍 اختبار حالات التعديل...")
    
    try:
        # قائمة الحالات المتوقعة
        expected_states = [
            "WAITING_EDIT_TRANSLATION_API",
            "WAITING_EDIT_TRANSLATION_MODEL",
            "WAITING_EDIT_TRANSLATION_PROMPT_TYPE",
            "WAITING_EDIT_TEXT_PROMPT",
            "WAITING_EDIT_IMAGE_PROMPT", 
            "WAITING_EDIT_TRANSLATION_START",
            "WAITING_CHANGE_TRANSLATION_LANG",
            "WAITING_EDIT_TRANSLATION_COST"
        ]
        
        for state in expected_states:
            # التحقق من تنسيق الحالة
            if state.startswith("WAITING_") and "_" in state:
                print(f"✅ {state} - تنسيق صحيح")
            else:
                print(f"❌ {state} - تنسيق خاطئ")
                return False
        
        print("✅ جميع حالات التعديل صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الحالات: {str(e)}")
        return False

def test_keyboard_structures():
    """اختبار بنية الكيبوردات الجديدة"""
    print("\n🔍 اختبار بنية الكيبوردات...")
    
    try:
        from telebot.types import ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
        
        # اختبار كيبورد اختيار نوع البرومبت
        prompt_type_keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        prompt_type_keyboard.add(
            KeyboardButton("📝 برومبت ترجمة النص"),
            KeyboardButton("🖼️ برومبت ترجمة الصور")
        )
        prompt_type_keyboard.add(KeyboardButton("❌ إلغاء"))
        
        # التحقق من الخصائص
        if prompt_type_keyboard.resize_keyboard and prompt_type_keyboard.one_time_keyboard:
            print("✅ كيبورد اختيار نوع البرومبت - خصائص صحيحة")
        else:
            print("❌ كيبورد اختيار نوع البرومبت - خصائص خاطئة")
            return False
        
        # التحقق من عدد الصفوف والأزرار
        if len(prompt_type_keyboard.keyboard) == 2:
            if len(prompt_type_keyboard.keyboard[0]) == 2 and len(prompt_type_keyboard.keyboard[1]) == 1:
                print("✅ كيبورد اختيار نوع البرومبت - بنية صحيحة")
            else:
                print("❌ كيبورد اختيار نوع البرومبت - بنية خاطئة")
                return False
        else:
            print("❌ كيبورد اختيار نوع البرومبت - عدد صفوف خاطئ")
            return False
        
        # اختبار كيبورد تغيير اللغة
        language_keyboard = InlineKeyboardMarkup()
        language_keyboard.add(
            InlineKeyboardButton("🇸🇦 العربية", callback_data="change_lang_to:ar:123"),
            InlineKeyboardButton("🇺🇸 الإنجليزية", callback_data="change_lang_to:en:123")
        )
        
        # التحقق من callback data
        for row in language_keyboard.keyboard:
            for button in row:
                if button.callback_data.startswith("change_lang_to:"):
                    parts = button.callback_data.split(":")
                    if len(parts) == 3 and parts[1] in ['ar', 'en'] and parts[2].isdigit():
                        print(f"✅ زر اللغة '{button.text}' - callback صحيح")
                    else:
                        print(f"❌ زر اللغة '{button.text}' - callback خاطئ")
                        return False
        
        print("✅ جميع بنى الكيبوردات صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكيبوردات: {str(e)}")
        return False

def test_database_schema():
    """اختبار مخطط قاعدة البيانات للبرومبتات"""
    print("\n🔍 اختبار مخطط قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # التحقق من جدول translation_global_settings
        cursor.execute("PRAGMA table_info(translation_global_settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = ['key', 'value', 'description']
        
        for column in required_columns:
            if column in columns:
                print(f"✅ عمود {column} موجود في translation_global_settings")
            else:
                print(f"❌ عمود {column} مفقود في translation_global_settings")
                conn.close()
                return False
        
        # اختبار إدراج وجلب البيانات
        test_key = "test_prompt_9999"
        test_value = "Test prompt value"
        test_description = "Test description"
        
        cursor.execute('''
            INSERT OR REPLACE INTO translation_global_settings (key, value, description)
            VALUES (?, ?, ?)
        ''', (test_key, test_value, test_description))
        
        cursor.execute('SELECT value FROM translation_global_settings WHERE key = ?', (test_key,))
        result = cursor.fetchone()
        
        if result and result[0] == test_value:
            print("✅ إدراج وجلب البيانات يعمل بشكل صحيح")
        else:
            print("❌ فشل في إدراج أو جلب البيانات")
            conn.close()
            return False
        
        # تنظيف بيانات الاختبار
        cursor.execute('DELETE FROM translation_global_settings WHERE key = ?', (test_key,))
        conn.commit()
        conn.close()
        
        print("✅ مخطط قاعدة البيانات صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات إدارة الترجمة"""
    print("🚀 بدء اختبار نظام إدارة الترجمة والبرومبتات المنفصلة")
    print("=" * 70)
    
    tests = [
        ("مخطط قاعدة البيانات", test_database_schema),
        ("نظام البرومبتات المنفصلة", test_translation_prompts_system),
        ("callback data للإدارة", test_translation_management_callbacks),
        ("حالات التعديل", test_translation_states),
        ("بنية الكيبوردات", test_keyboard_structures),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات إدارة الترجمة نجحت!")
        print("\n✨ الميزات الجديدة:")
        print("   ✅ أزرار إدارة الترجمة تعمل بشكل صحيح")
        print("   ✅ برومبتات منفصلة للنص والصور")
        print("   ✅ تعديل جميع إعدادات الترجمة")
        print("   ✅ كيبوردات محسنة للتفاعل")
        print("   ✅ حماية وتحقق من البيانات")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
