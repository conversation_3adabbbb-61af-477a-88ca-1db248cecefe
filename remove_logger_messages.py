#!/usr/bin/env python3
"""
سكريبت لإزالة جميع رسائل logger من الملفات
"""

import re
import os

def remove_logger_messages_from_file(file_path):
    """إزالة رسائل logger من ملف"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن أسطر logger وإزالتها
        lines = content.split('\n')
        new_lines = []
        
        for line in lines:
            # إذا كان السطر يحتوي على logger، استبدله بتعليق
            if 'logger.info(' in line:
                # استخراج النص من داخل logger.info
                match = re.search(r'logger\.info\((.*?)\)', line)
                if match:
                    indent = line[:len(line) - len(line.lstrip())]
                    new_lines.append(f"{indent}# تم تسجيل: {match.group(1)}")
                else:
                    new_lines.append(line.replace('logger.info(', '# logger.info('))
            elif 'logger.warning(' in line:
                match = re.search(r'logger\.warning\((.*?)\)', line)
                if match:
                    indent = line[:len(line) - len(line.lstrip())]
                    new_lines.append(f"{indent}# تحذير: {match.group(1)}")
                else:
                    new_lines.append(line.replace('logger.warning(', '# logger.warning('))
            elif 'logger.error(' in line:
                match = re.search(r'logger\.error\((.*?)\)', line)
                if match:
                    indent = line[:len(line) - len(line.lstrip())]
                    new_lines.append(f"{indent}# خطأ: {match.group(1)}")
                else:
                    new_lines.append(line.replace('logger.error(', '# logger.error('))
            elif 'logger.debug(' in line:
                match = re.search(r'logger\.debug\((.*?)\)', line)
                if match:
                    indent = line[:len(line) - len(line.lstrip())]
                    new_lines.append(f"{indent}# تشخيص: {match.group(1)}")
                else:
                    new_lines.append(line.replace('logger.debug(', '# logger.debug('))
            else:
                new_lines.append(line)
        
        # كتابة الملف المحدث
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_lines))
        
        print(f"✅ تم تحديث {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث {file_path}: {e}")
        return False

def main():
    """إزالة رسائل logger من الملفات المحددة"""
    files_to_update = [
        'professional_document_translator.py'
    ]
    
    print("🔧 بدء إزالة رسائل logger من الملفات...")
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            remove_logger_messages_from_file(file_path)
        else:
            print(f"⚠️ الملف غير موجود: {file_path}")
    
    print("✅ تم الانتهاء من إزالة رسائل logger")

if __name__ == "__main__":
    main()
