#!/usr/bin/env python3
"""
اختبار جميع الإصلاحات الشاملة لنظام الترجمة
"""

import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_error_message_hiding():
    """اختبار إخفاء رسائل الخطأ التقنية"""
    print("🔍 اختبار إخفاء رسائل الخطأ التقنية...")
    
    try:
        from text_extraction import text_extractor
        
        # اختبار رسالة خطأ tesseract المحسنة
        fake_image = b"fake_image_data"
        success, message = text_extractor.extract_text_from_image(fake_image)
        
        if not success:
            # التحقق من أن الرسالة لا تحتوي على تفاصيل تقنية مخيفة
            if "tesseract غير مثبت" not in message and "github.com" not in message:
                print("✅ رسالة الخطأ مبسطة ومناسبة للمستخدمين")
                print(f"   الرسالة: {message[:50]}...")
            else:
                print("❌ رسالة الخطأ ما زالت تحتوي على تفاصيل تقنية")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رسائل الخطأ: {str(e)}")
        return False

def test_arabic_text_direction():
    """اختبار إصلاح اتجاه النص العربي"""
    print("\n🔍 اختبار إصلاح اتجاه النص العربي...")
    
    try:
        from file_processing import file_processor
        
        # نص عربي مختلط مع إنجليزي
        test_text = "هذا نص عربي مع English text مختلط"
        
        # اختبار تنظيف النص
        clean_text = file_processor.clean_text_for_pdf(test_text)
        
        if clean_text:
            print("✅ تنظيف النص يعمل بشكل صحيح")
        else:
            print("❌ فشل في تنظيف النص")
            return False
        
        # اختبار إصلاح اتجاه النص العربي
        fixed_text = file_processor.fix_arabic_text_direction(test_text)
        
        if fixed_text:
            print("✅ إصلاح اتجاه النص العربي يعمل")
            print(f"   النص الأصلي: {test_text}")
            print(f"   النص المصلح: {fixed_text}")
        else:
            print("❌ فشل في إصلاح اتجاه النص العربي")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اتجاه النص العربي: {str(e)}")
        return False

def test_cancel_buttons():
    """اختبار أزرار الإلغاء والعودة"""
    print("\n🔍 اختبار أزرار الإلغاء والعودة...")
    
    try:
        # محاكاة النصوص المتوقعة لأزرار الإلغاء
        cancel_buttons = [
            "❌ إلغاء",
            "🏠 الرجوع إلى الرئيسية", 
            "🔙 الرجوع إلى السابقة"
        ]
        
        for button_text in cancel_buttons:
            # التحقق من أن النص يحتوي على الرموز الصحيحة
            if any(symbol in button_text for symbol in ["❌", "🏠", "🔙"]):
                print(f"✅ زر '{button_text}' - تنسيق صحيح")
            else:
                print(f"❌ زر '{button_text}' - تنسيق خاطئ")
                return False
        
        print("✅ جميع أزرار الإلغاء والعودة بتنسيق صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أزرار الإلغاء: {str(e)}")
        return False

def test_ai_restrictions_removal():
    """اختبار إزالة التقييدات من الذكاء الاصطناعي"""
    print("\n🔍 اختبار إزالة التقييدات من الذكاء الاصطناعي...")
    
    try:
        from translation_system import translation_system
        
        # اختبار البرومبت الافتراضي للعربية
        arabic_prompt = translation_system.get_arabic_translation_prompt()
        
        # التحقق من عدم وجود تقييدات مفرطة
        restrictive_phrases = [
            "CRITICAL SECURITY",
            "NEVER reveal",
            "أعتذر، لا يمكنني",
            "only a translation tool"
        ]
        
        has_restrictions = any(phrase in arabic_prompt for phrase in restrictive_phrases)
        
        if not has_restrictions:
            print("✅ تم إزالة التقييدات المفرطة من البرومبت العربي")
        else:
            print("❌ ما زالت هناك تقييدات مفرطة في البرومبت العربي")
            return False
        
        # اختبار البرومبت الافتراضي للإنجليزية
        english_prompt = translation_system.get_english_translation_prompt()
        
        has_restrictions = any(phrase in english_prompt for phrase in restrictive_phrases)
        
        if not has_restrictions:
            print("✅ تم إزالة التقييدات المفرطة من البرومبت الإنجليزي")
        else:
            print("❌ ما زالت هناك تقييدات مفرطة في البرومبت الإنجليزي")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إزالة التقييدات: {str(e)}")
        return False

def test_pdf_improvements():
    """اختبار تحسينات PDF"""
    print("\n🔍 اختبار تحسينات PDF...")
    
    try:
        from file_processing import file_processor
        
        # اختبار إنشاء تخطيط مختلط
        original_text = "This is original text.\nSecond paragraph."
        translated_text = "هذا نص مترجم.\nفقرة ثانية."
        
        mixed_layout = file_processor.create_mixed_layout(original_text, translated_text)
        
        if mixed_layout and "[ORIGINAL]" in mixed_layout and "[TRANSLATED]" in mixed_layout:
            print("✅ إنشاء التخطيط المختلط يعمل بشكل صحيح")
            print(f"   عدد الأحرف: {len(mixed_layout)}")
        else:
            print("❌ فشل في إنشاء التخطيط المختلط")
            return False
        
        # اختبار تنظيف النص من الأحرف المشكلة
        problematic_text = "نص\x00مع\u200eأحرف\ufeffمشكلة"
        clean_text = file_processor.clean_text_for_pdf(problematic_text)
        
        # التحقق من إزالة الأحرف المشكلة
        problematic_chars = ['\x00', '\u200e', '\ufeff']
        has_problematic = any(char in clean_text for char in problematic_chars)
        
        if not has_problematic:
            print("✅ تنظيف الأحرف المشكلة يعمل بشكل صحيح")
        else:
            print("❌ فشل في تنظيف الأحرف المشكلة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحسينات PDF: {str(e)}")
        return False

def test_auto_installation():
    """اختبار التثبيت التلقائي"""
    print("\n🔍 اختبار نظام التثبيت التلقائي...")
    
    try:
        from text_extraction import text_extractor
        
        # اختبار وجود دوال التثبيت التلقائي
        if hasattr(text_extractor, 'auto_install_tesseract'):
            print("✅ دالة التثبيت التلقائي موجودة")
        else:
            print("❌ دالة التثبيت التلقائي مفقودة")
            return False
        
        if hasattr(text_extractor, '_install_tesseract_windows'):
            print("✅ دالة تثبيت Windows موجودة")
        else:
            print("❌ دالة تثبيت Windows مفقودة")
            return False
        
        if hasattr(text_extractor, '_install_tesseract_linux'):
            print("✅ دالة تثبيت Linux موجودة")
        else:
            print("❌ دالة تثبيت Linux مفقودة")
            return False
        
        print("✅ نظام التثبيت التلقائي مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التثبيت التلقائي: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات الإصلاحات"""
    print("🚀 بدء اختبار جميع الإصلاحات الشاملة")
    print("=" * 70)
    
    tests = [
        ("إخفاء رسائل الخطأ التقنية", test_error_message_hiding),
        ("إصلاح اتجاه النص العربي", test_arabic_text_direction),
        ("أزرار الإلغاء والعودة", test_cancel_buttons),
        ("إزالة تقييدات الذكاء الاصطناعي", test_ai_restrictions_removal),
        ("تحسينات PDF", test_pdf_improvements),
        ("نظام التثبيت التلقائي", test_auto_installation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الإصلاحات نجحت!")
        print("\n✨ الإصلاحات المطبقة:")
        print("   ✅ إخفاء رسائل الخطأ التقنية عن المستخدمين")
        print("   ✅ تثبيت tesseract تلقائياً")
        print("   ✅ إصلاح الأحرف العربية المعكوسة في PDF")
        print("   ✅ إصلاح أزرار الإلغاء والعودة")
        print("   ✅ إزالة التقييدات من الذكاء الاصطناعي")
        print("   ✅ تحسين إنشاء ملفات PDF المترجمة")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
