#!/usr/bin/env python3
"""
اختبار أزرار الكيبورد لاختيار النموذج
"""

import os
import sys

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_selection_flow():
    """اختبار تدفق اختيار النموذج بأزرار الكيبورد"""
    print("🔍 اختبار تدفق اختيار النموذج...")
    
    try:
        # محاكاة الحالات
        user_states = {}
        user_inputs = {}
        user_id = 123
        
        # محاكاة حالة انتظار اختيار النموذج
        user_states[user_id] = "WAITING_TRANSLATION_MODEL"
        user_inputs[user_id] = {
            "translation_target_language": "ar",
            "translation_start_message": "مرحباً! أرسل النص للترجمة",
            "translation_api_keys": None
        }
        
        # قائمة النماذج المتاحة
        available_models = [
            "gemma-3n-e2b-it",
            "gemma-3-12b-it", 
            "learnlm-2.0-flash-experimental",
            "gemma-3-27b-it"
        ]
        
        # اختبار نموذج صحيح
        test_model = "gemma-3n-e2b-it"
        
        if test_model in available_models:
            print(f"✅ النموذج {test_model} متاح في القائمة")
            
            # محاكاة اختيار النموذج
            user_inputs[user_id]["translation_model"] = test_model
            user_states[user_id] = "WAITING_TRANSLATION_CUSTOM_PROMPT"
            
            print(f"✅ تم حفظ النموذج: {user_inputs[user_id]['translation_model']}")
            print(f"✅ تم تحديث الحالة إلى: {user_states[user_id]}")
            
        else:
            print(f"❌ النموذج {test_model} غير متاح")
            return False
        
        # اختبار نموذج غير صحيح
        invalid_model = "invalid-model"
        
        if invalid_model not in available_models:
            print(f"✅ النموذج غير الصحيح {invalid_model} تم رفضه بشكل صحيح")
        else:
            print(f"❌ النموذج غير الصحيح {invalid_model} تم قبوله خطأً")
            return False
        
        # اختبار جميع النماذج المتاحة
        print("\n📋 اختبار جميع النماذج المتاحة:")
        for model in available_models:
            print(f"   ✅ {model}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار النموذج: {str(e)}")
        return False

def test_keyboard_structure():
    """اختبار بنية الكيبورد"""
    print("\n🔍 اختبار بنية الكيبورد...")
    
    try:
        from telebot.types import ReplyKeyboardMarkup, KeyboardButton
        
        # إنشاء كيبورد مثل الذي في الكود
        model_keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        model_keyboard.add(
            KeyboardButton("gemma-3n-e2b-it"),
            KeyboardButton("gemma-3-12b-it")
        )
        model_keyboard.add(
            KeyboardButton("learnlm-2.0-flash-experimental"),
            KeyboardButton("gemma-3-27b-it")
        )
        
        # التحقق من خصائص الكيبورد
        if model_keyboard.resize_keyboard:
            print("✅ resize_keyboard مفعل")
        else:
            print("❌ resize_keyboard غير مفعل")
            return False
        
        if model_keyboard.one_time_keyboard:
            print("✅ one_time_keyboard مفعل")
        else:
            print("❌ one_time_keyboard غير مفعل")
            return False
        
        # التحقق من عدد الصفوف
        if len(model_keyboard.keyboard) == 2:
            print("✅ عدد الصفوف صحيح (2)")
        else:
            print(f"❌ عدد الصفوف خاطئ: {len(model_keyboard.keyboard)}")
            return False
        
        # التحقق من عدد الأزرار في كل صف
        if len(model_keyboard.keyboard[0]) == 2:
            print("✅ الصف الأول يحتوي على زرين")
        else:
            print(f"❌ الصف الأول يحتوي على {len(model_keyboard.keyboard[0])} أزرار")
            return False
        
        if len(model_keyboard.keyboard[1]) == 2:
            print("✅ الصف الثاني يحتوي على زرين")
        else:
            print(f"❌ الصف الثاني يحتوي على {len(model_keyboard.keyboard[1])} أزرار")
            return False
        
        # التحقق من نصوص الأزرار
        expected_buttons = [
            ["gemma-3n-e2b-it", "gemma-3-12b-it"],
            ["learnlm-2.0-flash-experimental", "gemma-3-27b-it"]
        ]
        
        for row_idx, row in enumerate(model_keyboard.keyboard):
            for btn_idx, button in enumerate(row):
                expected_text = expected_buttons[row_idx][btn_idx]
                if button.text == expected_text:
                    print(f"✅ زر '{expected_text}' في المكان الصحيح")
                else:
                    print(f"❌ زر '{button.text}' في مكان خاطئ، متوقع '{expected_text}'")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بنية الكيبورد: {str(e)}")
        return False

def test_prompt_keyboard():
    """اختبار كيبورد اختيار البرومبت"""
    print("\n🔍 اختبار كيبورد اختيار البرومبت...")
    
    try:
        from telebot.types import ReplyKeyboardMarkup, KeyboardButton
        
        # إنشاء كيبورد البرومبت
        prompt_keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        prompt_keyboard.add(KeyboardButton("افتراضي"))
        
        # التحقق من الخصائص
        if prompt_keyboard.resize_keyboard and prompt_keyboard.one_time_keyboard:
            print("✅ خصائص كيبورد البرومبت صحيحة")
        else:
            print("❌ خصائص كيبورد البرومبت خاطئة")
            return False
        
        # التحقق من وجود زر "افتراضي"
        if len(prompt_keyboard.keyboard) == 1 and len(prompt_keyboard.keyboard[0]) == 1:
            if prompt_keyboard.keyboard[0][0].text == "افتراضي":
                print("✅ زر 'افتراضي' موجود بشكل صحيح")
            else:
                print(f"❌ نص الزر خاطئ: '{prompt_keyboard.keyboard[0][0].text}'")
                return False
        else:
            print("❌ بنية كيبورد البرومبت خاطئة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كيبورد البرومبت: {str(e)}")
        return False

def test_state_transitions():
    """اختبار انتقالات الحالات"""
    print("\n🔍 اختبار انتقالات الحالات...")
    
    try:
        # محاكاة تدفق الحالات
        states_flow = [
            "WAITING_TRANSLATION_START_MESSAGE",
            "WAITING_TRANSLATION_API_KEYS", 
            "WAITING_TRANSLATION_MODEL",
            "WAITING_TRANSLATION_CUSTOM_PROMPT"
        ]
        
        current_state = "WAITING_TRANSLATION_API_KEYS"
        
        # الانتقال لاختيار النموذج
        if current_state == "WAITING_TRANSLATION_API_KEYS":
            next_state = "WAITING_TRANSLATION_MODEL"
            print(f"✅ انتقال من {current_state} إلى {next_state}")
        else:
            print(f"❌ انتقال خاطئ من {current_state}")
            return False
        
        # الانتقال لإعداد البرومبت
        current_state = next_state
        if current_state == "WAITING_TRANSLATION_MODEL":
            next_state = "WAITING_TRANSLATION_CUSTOM_PROMPT"
            print(f"✅ انتقال من {current_state} إلى {next_state}")
        else:
            print(f"❌ انتقال خاطئ من {current_state}")
            return False
        
        print("✅ جميع انتقالات الحالات صحيحة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار انتقالات الحالات: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات أزرار الكيبورد"""
    print("🚀 بدء اختبار أزرار الكيبورد لاختيار النموذج")
    print("=" * 60)
    
    tests = [
        ("تدفق اختيار النموذج", test_model_selection_flow),
        ("بنية الكيبورد", test_keyboard_structure),
        ("كيبورد البرومبت", test_prompt_keyboard),
        ("انتقالات الحالات", test_state_transitions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات أزرار الكيبورد نجحت!")
        print("\n✨ التحسينات المطبقة:")
        print("   ✅ أزرار اختيار النموذج أصبحت أزرار كيبورد عادية")
        print("   ✅ خاصية one_time_keyboard لإخفاء الكيبورد بعد الاختيار")
        print("   ✅ خاصية resize_keyboard لتحسين المظهر")
        print("   ✅ معالجة صحيحة للنماذج الصحيحة والخاطئة")
        print("   ✅ انتقالات سلسة بين الحالات")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
