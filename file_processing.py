"""
نظام معالجة الملفات المتقدم
يدعم معالجة الملفات صفحة بصفحة مع الحفاظ على التنسيق
"""

import os
import tempfile
import io
import json
from docx import Document
from docx.shared import Inches
import PyPDF2
import pdfplumber
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from PIL import Image
import sqlite3

class FileProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_arabic_fonts()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية للـ PDF"""
        try:
            # محاولة تحميل خط عربي من النظام
            arabic_font_paths = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/System/Library/Fonts/Arial.ttf',
                '/usr/share/fonts/TTF/arial.ttf'
            ]
            
            for font_path in arabic_font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('Arabic', font_path))
                        self.arabic_font = 'Arabic'
                        return
                    except:
                        continue
            
            # إذا لم يتم العثور على خط، استخدم الخط الافتراضي
            self.arabic_font = 'Helvetica'
            
        except Exception as e:
            self.arabic_font = 'Helvetica'
    
    def process_file_by_pages(self, file_data, file_name, translation_function):
        """معالجة الملف صفحة بصفحة مع الترجمة"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        if file_extension == '.pdf':
            return self.process_pdf_by_pages(file_data, file_name, translation_function)
        elif file_extension == '.docx':
            return self.process_docx_by_pages(file_data, file_name, translation_function)
        else:
            return False, "نوع الملف غير مدعوم للمعالجة المتقدمة"
    
    def process_pdf_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف PDF صفحة بصفحة"""
        try:
            # قراءة PDF الأصلي
            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                total_pages = len(pdf.pages)
                
                # إنشاء PDF جديد للترجمة
                output_buffer = io.BytesIO()
                c = canvas.Canvas(output_buffer, pagesize=A4)
                
                for page_num, page in enumerate(pdf.pages):
                    # استخراج النص من الصفحة
                    page_text = page.extract_text()
                    
                    if page_text and page_text.strip():
                        # ترجمة النص
                        success, translated_text = translation_function(page_text)
                        
                        if success:
                            # إضافة الصفحة المترجمة
                            self.add_text_to_pdf_page(c, translated_text, page_num + 1)
                        else:
                            # في حالة فشل الترجمة، أضف النص الأصلي
                            self.add_text_to_pdf_page(c, page_text, page_num + 1)
                    else:
                        # صفحة فارغة أو تحتوي على صور فقط
                        self.add_text_to_pdf_page(c, f"صفحة {page_num + 1} - لا يوجد نص للترجمة", page_num + 1)
                    
                    # إنهاء الصفحة
                    c.showPage()
                
                # حفظ PDF
                c.save()
                output_buffer.seek(0)
                
                return True, output_buffer.getvalue()
                
        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF: {str(e)}"
    
    def add_text_to_pdf_page(self, canvas_obj, text, page_num):
        """إضافة نص إلى صفحة PDF"""
        try:
            # إعداد الخط والحجم
            canvas_obj.setFont(self.arabic_font, 12)
            
            # تقسيم النص إلى أسطر
            lines = self.wrap_text(text, 70)  # 70 حرف لكل سطر
            
            # بدء الكتابة من أعلى الصفحة
            y_position = 750
            
            # إضافة رقم الصفحة
            canvas_obj.drawString(50, 800, f"Page {page_num}")
            
            # إضافة النص سطر بسطر
            for line in lines:
                if y_position < 50:  # إذا وصلنا لنهاية الصفحة
                    break
                
                canvas_obj.drawString(50, y_position, line)
                y_position -= 15  # المسافة بين الأسطر
                
        except Exception as e:
            # في حالة الخطأ، أضف نص بسيط
            canvas_obj.drawString(50, 400, f"Error displaying text on page {page_num}")
    
    def wrap_text(self, text, width):
        """تقسيم النص إلى أسطر بعرض محدد"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def process_docx_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف Word صفحة بصفحة"""
        try:
            # قراءة المستند الأصلي
            doc = Document(io.BytesIO(file_data))
            
            # إنشاء مستند جديد للترجمة
            new_doc = Document()
            
            # معالجة الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # ترجمة النص
                    success, translated_text = translation_function(paragraph.text)
                    
                    if success:
                        # إضافة الفقرة المترجمة
                        new_paragraph = new_doc.add_paragraph(translated_text)
                        # نسخ التنسيق إذا أمكن
                        try:
                            new_paragraph.style = paragraph.style
                        except:
                            pass
                    else:
                        # في حالة فشل الترجمة، أضف النص الأصلي
                        new_doc.add_paragraph(paragraph.text)
                else:
                    # فقرة فارغة
                    new_doc.add_paragraph("")
            
            # معالجة الجداول
            for table in doc.tables:
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))
                
                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        if cell.text.strip():
                            # ترجمة نص الخلية
                            success, translated_text = translation_function(cell.text)
                            new_table.cell(i, j).text = translated_text if success else cell.text
                        else:
                            new_table.cell(i, j).text = ""
            
            # حفظ المستند الجديد
            output_buffer = io.BytesIO()
            new_doc.save(output_buffer)
            output_buffer.seek(0)
            
            return True, output_buffer.getvalue()
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف Word: {str(e)}"
    
    def extract_images_from_pdf(self, file_data):
        """استخراج الصور من ملف PDF (للحفاظ عليها دون ترجمة)"""
        try:
            images = []
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
            
            for page_num, page in enumerate(pdf_reader.pages):
                if '/XObject' in page['/Resources']:
                    xObject = page['/Resources']['/XObject'].get_object()
                    
                    for obj in xObject:
                        if xObject[obj]['/Subtype'] == '/Image':
                            # استخراج الصورة
                            try:
                                image_data = xObject[obj].get_data()
                                images.append({
                                    'page': page_num + 1,
                                    'data': image_data,
                                    'name': f"image_page_{page_num + 1}_{obj}"
                                })
                            except:
                                continue
            
            return images
            
        except Exception as e:
            return []
    
    def create_translation_progress_file(self, file_name, total_pages):
        """إنشاء ملف لتتبع تقدم الترجمة"""
        progress_data = {
            'file_name': file_name,
            'total_pages': total_pages,
            'completed_pages': 0,
            'status': 'in_progress',
            'created_at': str(os.time.time())
        }
        
        progress_file = os.path.join(self.temp_dir, f"progress_{file_name}.json")
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
        
        return progress_file
    
    def update_translation_progress(self, progress_file, completed_pages):
        """تحديث تقدم الترجمة"""
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            progress_data['completed_pages'] = completed_pages
            progress_data['status'] = 'completed' if completed_pages >= progress_data['total_pages'] else 'in_progress'
            
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            return True
        except:
            return False
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = tempfile.mkdtemp()
        except:
            pass
    
    def get_file_page_count(self, file_data, file_name):
        """الحصول على عدد صفحات الملف"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        try:
            if file_extension == '.pdf':
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
                return len(pdf_reader.pages)
            elif file_extension == '.docx':
                # تقدير عدد الصفحات بناءً على عدد الفقرات
                doc = Document(io.BytesIO(file_data))
                paragraph_count = len([p for p in doc.paragraphs if p.text.strip()])
                estimated_pages = max(1, paragraph_count // 20)  # تقدير 20 فقرة لكل صفحة
                return estimated_pages
            else:
                return 1
        except:
            return 1

# إنشاء مثيل عام للاستخدام
file_processor = FileProcessor()
