"""
نظام معالجة الملفات المتقدم
يدعم معالجة الملفات صفحة بصفحة مع الحفاظ على التنسيق
"""

import os
import tempfile
import io
import json
import re
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
import PyPDF2
import pdfplumber
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.enums import TA_RIGHT, TA_LEFT, TA_CENTER
from PIL import Image
import sqlite3
import fitz  # PyMuPDF للتعديل المباشر للـ PDF

class FileProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_arabic_fonts()
        # إعداد مكتبات إصلاح النص العربي
        self.setup_arabic_text_processing()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية للـ PDF مع دعم كامل للنص العربي"""
        try:
            import platform
            system = platform.system().lower()

            # تثبيت خط عربي مدمج إذا لم يوجد
            self.install_arabic_font_if_needed()

            # مسارات الخطوط العربية المتخصصة
            if system == 'windows':
                arabic_font_paths = [
                    r'C:\Windows\Fonts\arial.ttf',
                    r'C:\Windows\Fonts\tahoma.ttf',
                    r'C:\Windows\Fonts\calibri.ttf',
                    # خطوط عربية متخصصة
                    r'C:\Windows\Fonts\NotoSansArabic-Regular.ttf',
                    r'C:\Windows\Fonts\AmiriRegular.ttf'
                ]
            elif system == 'darwin':  # macOS
                arabic_font_paths = [
                    '/System/Library/Fonts/Arial.ttf',
                    '/Library/Fonts/Arial.ttf',
                    '/System/Library/Fonts/Helvetica.ttc'
                ]
            else:  # Linux
                arabic_font_paths = [
                    '/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf',
                    '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                    '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                    '/usr/share/fonts/TTF/arial.ttf'
                ]

            # محاولة تسجيل خط عربي مناسب
            for font_path in arabic_font_paths:
                if os.path.exists(font_path):
                    try:
                        # تسجيل الخط مع دعم Unicode كامل
                        pdfmetrics.registerFont(TTFont('ArabicFont', font_path, validate=False))
                        self.arabic_font = 'ArabicFont'
                        print(f"تم تسجيل الخط العربي: {font_path}")
                        return
                    except Exception as font_error:
                        continue

            # إذا فشل كل شيء، استخدم خط افتراضي مع دعم Unicode
            try:
                from reportlab.lib.fonts import addMapping
                pdfmetrics.registerFont(TTFont('DefaultArabic', self.get_embedded_arabic_font()))
                addMapping('DefaultArabic', 0, 0, 'DefaultArabic')
                self.arabic_font = 'DefaultArabic'
            except:
                self.arabic_font = 'Helvetica'

        except Exception as e:
            self.arabic_font = 'Helvetica'

    def install_arabic_font_if_needed(self):
        """تثبيت خط عربي إذا لم يوجد"""
        try:
            import urllib.request
            import tempfile

            # تحميل خط Noto Sans Arabic إذا لم يوجد
            font_url = "https://fonts.google.com/download?family=Noto%20Sans%20Arabic"

            # فحص وجود خط عربي
            import platform
            system = platform.system().lower()

            if system == 'windows':
                font_dir = r'C:\Windows\Fonts'
                if not any(os.path.exists(os.path.join(font_dir, f)) for f in ['NotoSansArabic-Regular.ttf', 'arial.ttf']):
                    # تحميل وتثبيت خط عربي
                    self.download_and_install_arabic_font()

        except Exception as e:
            pass  # تجاهل الأخطاء والمتابعة

    def get_embedded_arabic_font(self):
        """الحصول على خط عربي مدمج"""
        # إنشاء خط افتراضي بسيط يدعم العربية
        try:
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # استخدام خط النظام الافتراضي
            return "Helvetica"
        except:
            return "Helvetica"

    def setup_arabic_text_processing(self):
        """إعداد مكتبات معالجة النص العربي"""
        try:
            # محاولة استيراد مكتبات إصلاح النص العربي
            try:
                import arabic_reshaper
                self.arabic_reshaper = arabic_reshaper
                print("✅ تم تحميل arabic_reshaper")
            except ImportError:
                print("⚠️ arabic_reshaper غير متوفر، سيتم استخدام حل بديل")
                self.arabic_reshaper = None

            try:
                from bidi.algorithm import get_display
                self.bidi_get_display = get_display
                print("✅ تم تحميل python-bidi")
            except ImportError:
                print("⚠️ python-bidi غير متوفر، سيتم استخدام حل بديل")
                self.bidi_get_display = None

        except Exception as e:
            print(f"⚠️ خطأ في إعداد معالجة النص العربي: {str(e)}")
            self.arabic_reshaper = None
            self.bidi_get_display = None

    def fix_arabic_text_for_pdf(self, text):
        """إصلاح النص العربي للعرض الصحيح في PDF"""
        try:
            if not text or not text.strip():
                return text

            # تنظيف النص من الأحرف المشكلة
            cleaned_text = self.clean_arabic_text(text)

            # إصلاح تشكيل الأحرف العربية
            if self.arabic_reshaper:
                try:
                    reshaped_text = self.arabic_reshaper.reshape(cleaned_text)
                except Exception as e:
                    print(f"خطأ في arabic_reshaper: {str(e)}")
                    reshaped_text = cleaned_text
            else:
                reshaped_text = self.manual_arabic_reshape(cleaned_text)

            # إصلاح اتجاه النص
            if self.bidi_get_display:
                try:
                    final_text = self.bidi_get_display(reshaped_text)
                except Exception as e:
                    print(f"خطأ في bidi: {str(e)}")
                    final_text = self.manual_bidi_fix(reshaped_text)
            else:
                final_text = self.manual_bidi_fix(reshaped_text)

            return final_text

        except Exception as e:
            print(f"خطأ في إصلاح النص العربي: {str(e)}")
            return text

    def clean_arabic_text(self, text):
        """تنظيف النص العربي من الأحرف المشكلة"""
        try:
            # إزالة الأحرف التحكمية
            text = re.sub(r'[\u200e\u200f\u202a\u202b\u202c\u202d\u202e]', '', text)

            # إزالة Zero Width characters
            text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)

            # تنظيف المسافات الزائدة
            text = re.sub(r'\s+', ' ', text)

            return text.strip()

        except Exception as e:
            print(f"خطأ في تنظيف النص: {str(e)}")
            return text

    def manual_arabic_reshape(self, text):
        """إعادة تشكيل النص العربي يدوياً (حل بديل)"""
        try:
            # حل بديل بسيط لإعادة تشكيل النص العربي
            # إزالة المسافات الزائدة بين الأحرف العربية
            arabic_pattern = r'([\u0600-\u06FF])\s+([\u0600-\u06FF])'
            while re.search(arabic_pattern, text):
                text = re.sub(arabic_pattern, r'\1\2', text)

            return text

        except Exception as e:
            print(f"خطأ في إعادة التشكيل اليدوي: {str(e)}")
            return text

    def manual_bidi_fix(self, text):
        """إصلاح اتجاه النص يدوياً (حل بديل)"""
        try:
            # للنص المختلط، نحتاج لمعالجة خاصة
            lines = text.split('\n')
            fixed_lines = []

            for line in lines:
                if self.detect_text_direction(line) == 'RTL':
                    # معالجة النص العربي
                    fixed_line = self.process_rtl_line(line)
                    fixed_lines.append(fixed_line)
                else:
                    # النص الإنجليزي يبقى كما هو
                    fixed_lines.append(line)

            return '\n'.join(fixed_lines)

        except Exception as e:
            print(f"خطأ في إصلاح الاتجاه اليدوي: {str(e)}")
            return text

    def process_rtl_line(self, line):
        """معالجة سطر نص عربي (RTL)"""
        try:
            # فصل الكلمات
            words = line.split()
            processed_words = []

            for word in words:
                if self.is_arabic_word(word):
                    # الكلمة العربية - لا نعكسها لأن reportlab يتعامل معها
                    processed_words.append(word)
                else:
                    # كلمة إنجليزية أو رقم
                    processed_words.append(word)

            # إعادة ترتيب الكلمات للنص العربي (من اليمين لليسار)
            return ' '.join(processed_words)

        except Exception as e:
            print(f"خطأ في معالجة السطر RTL: {str(e)}")
            return line

    def is_arabic_char(self, char):
        """فحص ما إذا كان الحرف عربياً"""
        try:
            return '\u0600' <= char <= '\u06FF' or '\u0750' <= char <= '\u077F'
        except:
            return False

    def is_arabic_word(self, word):
        """فحص ما إذا كانت الكلمة عربية"""
        try:
            arabic_chars = sum(1 for char in word if self.is_arabic_char(char))
            total_chars = sum(1 for char in word if char.isalpha())

            if total_chars == 0:
                return False

            return arabic_chars / total_chars > 0.5

        except:
            return False

    def detect_text_direction(self, text):
        """كشف اتجاه النص (RTL أم LTR)"""
        try:
            arabic_count = 0
            total_chars = 0

            for char in text:
                if char.isalpha():
                    total_chars += 1
                    if self.is_arabic_char(char):
                        arabic_count += 1

            if total_chars == 0:
                return 'LTR'

            # إذا كان أكثر من 30% من النص عربي، فهو RTL
            arabic_ratio = arabic_count / total_chars
            return 'RTL' if arabic_ratio > 0.3 else 'LTR'

        except Exception as e:
            print(f"خطأ في كشف اتجاه النص: {str(e)}")
            return 'LTR'

    def fix_arabic_text_advanced(self, text):
        """إصلاح النص العربي المتقدم مع الحفاظ على التنسيق الأصلي"""
        try:
            if not text or not text.strip():
                return text

            # تنظيف النص من الأحرف المشكلة
            cleaned_text = self.clean_arabic_text(text)

            # إصلاح تشكيل الأحرف العربية باستخدام arabic_reshaper
            if self.arabic_reshaper:
                try:
                    reshaped_text = self.arabic_reshaper.reshape(cleaned_text)
                except Exception as e:
                    print(f"خطأ في arabic_reshaper: {str(e)}")
                    reshaped_text = cleaned_text
            else:
                reshaped_text = self.manual_arabic_reshape(cleaned_text)

            # إصلاح اتجاه النص باستخدام python-bidi
            if self.bidi_get_display:
                try:
                    final_text = self.bidi_get_display(reshaped_text)
                except Exception as e:
                    print(f"خطأ في bidi: {str(e)}")
                    final_text = reshaped_text
            else:
                final_text = reshaped_text

            return final_text

        except Exception as e:
            print(f"خطأ في إصلاح النص العربي المتقدم: {str(e)}")
            return text

    def process_file_inplace(self, file_data, file_name, translation_function):
        """معالجة الملف مع التعديل المباشر والحفاظ على التنسيق الأصلي"""
        file_extension = os.path.splitext(file_name.lower())[1]

        if file_extension == '.pdf':
            return self.process_pdf_inplace(file_data, file_name, translation_function)
        elif file_extension == '.docx':
            return self.process_docx_inplace(file_data, file_name, translation_function)
        else:
            return False, "نوع الملف غير مدعوم للمعالجة المباشرة"

    def process_pdf_inplace(self, file_data, file_name, translation_function):
        """تعديل ملف PDF مباشرة مع الحفاظ على التنسيق الأصلي"""
        try:
            # فتح PDF باستخدام PyMuPDF
            doc = fitz.open(stream=file_data, filetype="pdf")

            # معالجة كل صفحة
            for page_num in range(len(doc)):
                page = doc[page_num]

                # استخراج النصوص مع معلومات الموقع والتنسيق
                text_instances = page.get_text("dict")

                # جمع النصوص للترجمة
                texts_to_translate = []
                text_locations = []

                # معالجة كل كتلة نص
                for block in text_instances["blocks"]:
                    if "lines" in block:  # كتلة نص
                        for line in block["lines"]:
                            for span in line["spans"]:
                                original_text = span["text"]

                                if original_text.strip():
                                    texts_to_translate.append(original_text)
                                    text_locations.append({
                                        'text': original_text,
                                        'bbox': span["bbox"],
                                        'font_size': span["size"],
                                        'font_name': span["font"],
                                        'flags': span.get("flags", 0)
                                    })

                # ترجمة النصوص
                translated_texts = []
                for text in texts_to_translate:
                    success, translated_text = translation_function(text)
                    if success:
                        # إصلاح النص العربي
                        fixed_text = self.fix_arabic_text_advanced(translated_text)
                        translated_texts.append(fixed_text)
                    else:
                        translated_texts.append(text)  # الاحتفاظ بالنص الأصلي في حالة فشل الترجمة

                # استبدال النصوص في الصفحة
                self.replace_texts_in_pdf_page(page, text_locations, translated_texts)

            # حفظ PDF المعدل
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            doc.close()

            output_buffer.seek(0)
            return True, output_buffer.getvalue()

        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF مباشرة: {str(e)}"

    def replace_texts_in_pdf_page(self, page, text_locations, translated_texts):
        """استبدال النصوص في صفحة PDF مع الحفاظ على التنسيق"""
        try:
            # حذف النصوص الأصلية أولاً
            for location in text_locations:
                bbox = location['bbox']
                rect = fitz.Rect(bbox)
                page.add_redact_annot(rect)

            # تطبيق الحذف
            page.apply_redactions()

            # إضافة النصوص المترجمة
            for i, location in enumerate(text_locations):
                if i < len(translated_texts):
                    translated_text = translated_texts[i]
                    bbox = location['bbox']
                    font_size = location['font_size']
                    font_name = location['font_name']

                    # تحديد اتجاه النص
                    text_direction = self.detect_text_direction(translated_text)

                    # حساب موقع النص
                    x = bbox[0]
                    y = bbox[1] + font_size * 0.8  # تعديل الموقع العمودي

                    if text_direction == 'RTL':
                        # للنص العربي - محاذاة من اليمين
                        x = bbox[2]  # البداية من اليمين

                    # إدراج النص المترجم
                    page.insert_text(
                        (x, y),
                        translated_text,
                        fontsize=font_size,
                        color=(0, 0, 0)
                    )

        except Exception as e:
            print(f"خطأ في استبدال النصوص في PDF: {str(e)}")

    def process_docx_inplace(self, file_data, file_name, translation_function):
        """تعديل ملف Word مباشرة مع الحفاظ على التنسيق الأصلي"""
        try:
            # قراءة المستند الأصلي
            doc = Document(io.BytesIO(file_data))

            # معالجة الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    original_text = paragraph.text

                    # ترجمة النص
                    success, translated_text = translation_function(original_text)

                    if success:
                        # إصلاح النص العربي
                        fixed_text = self.fix_arabic_text_advanced(translated_text)

                        # تحديد اتجاه النص
                        text_direction = self.detect_text_direction(fixed_text)

                        # الحفاظ على التنسيق الأصلي
                        original_runs = list(paragraph.runs)

                        # مسح النص الحالي
                        paragraph.clear()

                        # إضافة النص المترجم مع التنسيق الأصلي
                        new_run = paragraph.add_run(fixed_text)

                        # نسخ التنسيق من أول run أصلي
                        if original_runs:
                            first_run = original_runs[0]
                            new_run.font.name = first_run.font.name
                            new_run.font.size = first_run.font.size
                            new_run.bold = first_run.bold
                            new_run.italic = first_run.italic
                            new_run.underline = first_run.underline

                        # تطبيق اتجاه النص المناسب
                        if text_direction == 'RTL':
                            # تطبيق اتجاه من اليمين لليسار للنص العربي
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                            try:
                                paragraph._element.set(qn('w:bidi'), '1')
                            except:
                                pass
                        else:
                            # اتجاه من اليسار لليمين للنص الإنجليزي
                            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # معالجة الجداول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                original_text = paragraph.text

                                # ترجمة النص
                                success, translated_text = translation_function(original_text)

                                if success:
                                    # إصلاح النص العربي
                                    fixed_text = self.fix_arabic_text_advanced(translated_text)

                                    # تحديد اتجاه النص
                                    text_direction = self.detect_text_direction(fixed_text)

                                    # الحفاظ على التنسيق الأصلي
                                    original_runs = list(paragraph.runs)

                                    # مسح النص الحالي
                                    paragraph.clear()

                                    # إضافة النص المترجم
                                    new_run = paragraph.add_run(fixed_text)

                                    # نسخ التنسيق
                                    if original_runs:
                                        first_run = original_runs[0]
                                        new_run.font.name = first_run.font.name
                                        new_run.font.size = first_run.font.size
                                        new_run.bold = first_run.bold
                                        new_run.italic = first_run.italic

                                    # تطبيق اتجاه النص
                                    if text_direction == 'RTL':
                                        paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                                        try:
                                            paragraph._element.set(qn('w:bidi'), '1')
                                        except:
                                            pass
                                    else:
                                        paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # حفظ المستند المعدل
            output_buffer = io.BytesIO()
            doc.save(output_buffer)

            output_buffer.seek(0)
            return True, output_buffer.getvalue()

        except Exception as e:
            return False, f"خطأ في معالجة ملف Word مباشرة: {str(e)}"
    
    def process_file_by_pages(self, file_data, file_name, translation_function):
        """معالجة الملف صفحة بصفحة مع الترجمة"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        if file_extension == '.pdf':
            return self.process_pdf_by_pages(file_data, file_name, translation_function)
        elif file_extension == '.docx':
            return self.process_docx_by_pages(file_data, file_name, translation_function)
        else:
            return False, "نوع الملف غير مدعوم للمعالجة المتقدمة"
    
    def process_pdf_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف PDF صفحة بصفحة مع الحفاظ على التنسيق"""
        try:
            # قراءة PDF الأصلي
            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                total_pages = len(pdf.pages)

                # إنشاء PDF جديد للترجمة
                output_buffer = io.BytesIO()
                c = canvas.Canvas(output_buffer, pagesize=A4)

                for page_num, page in enumerate(pdf.pages):
                    # استخراج النص من الصفحة مع الحفاظ على التخطيط
                    page_text = page.extract_text()

                    if page_text and page_text.strip():
                        # ترجمة النص
                        success, translated_text = translation_function(page_text)

                        if success:
                            # إضافة الصفحة المترجمة مع تحسين التنسيق
                            self.add_formatted_text_to_pdf_page(c, translated_text, page_text, page_num + 1)
                        else:
                            # في حالة فشل الترجمة، أضف النص الأصلي
                            self.add_formatted_text_to_pdf_page(c, page_text, page_text, page_num + 1)
                    else:
                        # صفحة فارغة أو تحتوي على صور فقط
                        self.add_text_to_pdf_page(c, f"صفحة {page_num + 1} - لا يوجد نص للترجمة", page_num + 1)

                    # إنهاء الصفحة
                    c.showPage()

                # حفظ PDF
                c.save()
                output_buffer.seek(0)

                return True, output_buffer.getvalue()

        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF: {str(e)}"
    
    def add_text_to_pdf_page(self, canvas_obj, text, page_num):
        """إضافة نص إلى صفحة PDF مع إصلاح النص العربي"""
        try:
            # محاولة إعداد الخط مع معالجة الأخطاء
            try:
                canvas_obj.setFont(self.arabic_font, 12)
            except Exception as font_error:
                # إذا فشل الخط المخصص، استخدم الخط الافتراضي
                canvas_obj.setFont('Helvetica', 12)
                self.arabic_font = 'Helvetica'

            # إصلاح النص العربي قبل المعالجة
            fixed_text = self.fix_arabic_text_for_pdf(text)

            # تنظيف النص من الأحرف المشكلة
            clean_text = self.clean_text_for_pdf(fixed_text)

            # تقسيم النص إلى أسطر
            lines = self.wrap_text(clean_text, 70)  # 70 حرف لكل سطر

            # بدء الكتابة من أعلى الصفحة
            y_position = 750

            # إضافة رقم الصفحة
            try:
                canvas_obj.drawString(50, 800, f"Page {page_num}")
            except:
                canvas_obj.drawString(50, 800, f"Page {page_num}".encode('ascii', 'ignore').decode())

            # إضافة النص سطر بسطر
            for line in lines:
                if y_position < 50:  # إذا وصلنا لنهاية الصفحة
                    break

                try:
                    canvas_obj.drawString(50, y_position, line)
                except Exception as line_error:
                    # في حالة فشل كتابة السطر، حاول مع تنظيف إضافي
                    try:
                        safe_line = line.encode('ascii', 'ignore').decode()
                        canvas_obj.drawString(50, y_position, safe_line)
                    except:
                        # إذا فشل كل شيء، اكتب رسالة بديلة
                        canvas_obj.drawString(50, y_position, "[Text encoding error]")

                y_position -= 15  # المسافة بين الأسطر

        except Exception as e:
            # في حالة الخطأ العام، أضف نص بسيط
            try:
                canvas_obj.setFont('Helvetica', 10)
                canvas_obj.drawString(50, 400, f"Error displaying text on page {page_num}")
                canvas_obj.drawString(50, 380, "Text content could not be rendered properly")
            except:
                pass  # تجاهل الأخطاء في حالة الطوارئ

    def clean_text_for_pdf(self, text):
        """تنظيف النص للتأكد من توافقه مع PDF مع إصلاح الأحرف العربية المتقطعة"""
        try:
            import re

            # إزالة الأحرف التحكمية
            text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

            # استبدال الأحرف المشكلة
            text = text.replace('\u200e', '')  # Left-to-right mark
            text = text.replace('\u200f', '')  # Right-to-left mark
            text = text.replace('\ufeff', '')  # Byte order mark
            text = text.replace('\u202a', '')  # Left-to-right embedding
            text = text.replace('\u202b', '')  # Right-to-left embedding
            text = text.replace('\u202c', '')  # Pop directional formatting

            # إصلاح النص العربي المتقطع
            text = self.fix_arabic_text_connectivity(text)

            # إصلاح اتجاه النص العربي
            text = self.fix_arabic_text_direction(text)

            # تطبيق تشكيل النص العربي الصحيح
            text = self.apply_arabic_shaping(text)

            # التأكد من أن النص قابل للترميز
            text = text.encode('utf-8', 'ignore').decode('utf-8')

            return text

        except Exception as e:
            # في حالة فشل التنظيف، أرجع النص كما هو
            return str(text)

    def fix_arabic_text_connectivity(self, text):
        """إصلاح مشكلة الأحرف العربية المتقطعة"""
        try:
            import re

            # إزالة المسافات الزائدة بين الأحرف العربية
            arabic_pattern = r'([\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF])\s+([\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF])'

            # دمج الأحرف العربية المتقطعة
            while re.search(arabic_pattern, text):
                text = re.sub(arabic_pattern, r'\1\2', text)

            # إصلاح الكلمات المقسمة بمسافات
            text = self.merge_split_arabic_words(text)

            return text

        except Exception as e:
            return text

    def merge_split_arabic_words(self, text):
        """دمج الكلمات العربية المقسمة"""
        try:
            import re

            # البحث عن أنماط الكلمات المقسمة
            lines = text.split('\n')
            fixed_lines = []

            for line in lines:
                # إزالة المسافات الزائدة
                line = re.sub(r'\s+', ' ', line.strip())

                # دمج الأحرف العربية المتتالية
                words = line.split(' ')
                merged_words = []
                current_arabic_word = ""

                for word in words:
                    if self.is_arabic_text(word):
                        current_arabic_word += word
                    else:
                        if current_arabic_word:
                            merged_words.append(current_arabic_word)
                            current_arabic_word = ""
                        merged_words.append(word)

                if current_arabic_word:
                    merged_words.append(current_arabic_word)

                fixed_lines.append(' '.join(merged_words))

            return '\n'.join(fixed_lines)

        except Exception as e:
            return text

    def is_arabic_text(self, text):
        """فحص ما إذا كان النص عربياً"""
        try:
            import re
            arabic_pattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]'
            return bool(re.search(arabic_pattern, text))
        except:
            return False

    def apply_arabic_shaping(self, text):
        """تطبيق تشكيل النص العربي الصحيح"""
        try:
            # محاولة استخدام مكتبة arabic-reshaper إذا كانت متوفرة
            try:
                import arabic_reshaper
                reshaped_text = arabic_reshaper.reshape(text)
                return reshaped_text
            except ImportError:
                # إذا لم تكن المكتبة متوفرة، استخدم حل بديل
                return self.basic_arabic_shaping(text)

        except Exception as e:
            return text

    def basic_arabic_shaping(self, text):
        """تشكيل أساسي للنص العربي"""
        try:
            # حل بديل بسيط لتشكيل النص العربي
            # إزالة المسافات الزائدة بين الأحرف العربية
            import re

            # دمج الأحرف العربية المتتالية
            text = re.sub(r'([\u0600-\u06FF])\s+([\u0600-\u06FF])', r'\1\2', text)

            return text

        except Exception as e:
            return text

    def fix_arabic_text_direction(self, text):
        """إصلاح اتجاه النص العربي المعكوس"""
        try:
            # استخدام مكتبة python-bidi لإصلاح اتجاه النص
            try:
                from bidi.algorithm import get_display
                # إصلاح اتجاه النص العربي
                text = get_display(text)
            except ImportError:
                # إذا لم تكن مكتبة bidi متوفرة، استخدم حل بديل
                text = self.fix_arabic_manual(text)

            return text

        except Exception as e:
            return text

    def fix_arabic_manual(self, text):
        """إصلاح اتجاه النص العربي يدوياً"""
        try:
            import re

            # تحديد النص العربي
            arabic_pattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+'

            def reverse_arabic_words(match):
                arabic_text = match.group()
                # عكس ترتيب الكلمات العربية فقط
                words = arabic_text.split()
                return ' '.join(reversed(words))

            # تطبيق الإصلاح على النص العربي فقط
            fixed_text = re.sub(arabic_pattern, reverse_arabic_words, text)

            return fixed_text

        except Exception as e:
            return text

    def add_formatted_text_to_pdf_page(self, canvas_obj, translated_text, original_text, page_num):
        """إضافة نص منسق إلى صفحة PDF مع الحفاظ على التخطيط"""
        try:
            # محاولة إعداد الخط مع معالجة الأخطاء
            try:
                canvas_obj.setFont(self.arabic_font, 12)
            except Exception as font_error:
                canvas_obj.setFont('Helvetica', 12)
                self.arabic_font = 'Helvetica'

            # تنظيف النص المترجم
            clean_translated = self.clean_text_for_pdf(translated_text)
            clean_original = self.clean_text_for_pdf(original_text)

            # إنشاء تخطيط مختلط (أصلي ومترجم)
            mixed_content = self.create_mixed_layout(clean_original, clean_translated)

            # تقسيم النص إلى أسطر
            lines = self.wrap_text(mixed_content, 70)

            # بدء الكتابة من أعلى الصفحة
            y_position = 750

            # إضافة رقم الصفحة والعنوان
            try:
                canvas_obj.drawString(50, 800, f"Page {page_num} - Translated Document")
            except:
                canvas_obj.drawString(50, 800, f"Page {page_num}")

            # إضافة النص سطر بسطر
            for line in lines:
                if y_position < 50:  # إذا وصلنا لنهاية الصفحة
                    break

                try:
                    canvas_obj.drawString(50, y_position, line)
                except Exception as line_error:
                    try:
                        safe_line = line.encode('ascii', 'ignore').decode()
                        canvas_obj.drawString(50, y_position, safe_line)
                    except:
                        canvas_obj.drawString(50, y_position, "[Text encoding error]")

                y_position -= 15

        except Exception as e:
            # في حالة الخطأ، استخدم الطريقة البسيطة
            self.add_text_to_pdf_page(canvas_obj, translated_text, page_num)

    def create_mixed_layout(self, original_text, translated_text):
        """إنشاء تخطيط نظيف للنص المترجم مع إصلاح النص العربي"""
        try:
            # إصلاح النص العربي قبل الإرجاع
            fixed_text = self.fix_arabic_text_for_pdf(translated_text.strip())
            return fixed_text

        except Exception as e:
            # في حالة الخطأ، أرجع النص المترجم فقط
            print(f"خطأ في إصلاح النص: {str(e)}")
            return translated_text.strip()
    
    def wrap_text(self, text, width):
        """تقسيم النص إلى أسطر بعرض محدد"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def process_docx_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف Word صفحة بصفحة"""
        try:
            # قراءة المستند الأصلي
            doc = Document(io.BytesIO(file_data))
            
            # إنشاء مستند جديد للترجمة
            new_doc = Document()
            
            # معالجة الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # ترجمة النص
                    success, translated_text = translation_function(paragraph.text)
                    
                    if success:
                        # إصلاح النص العربي قبل الإضافة
                        fixed_text = self.fix_arabic_text_for_pdf(translated_text)

                        # إضافة الفقرة المترجمة
                        new_paragraph = new_doc.add_paragraph(fixed_text)

                        # تحديد اتجاه النص حسب المحتوى
                        text_direction = self.detect_text_direction(fixed_text)

                        # تطبيق اتجاه النص المناسب
                        if text_direction == 'RTL':
                            # تطبيق اتجاه من اليمين لليسار للنص العربي
                            new_paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                            # إضافة خصائص RTL إذا كانت متوفرة
                            try:
                                new_paragraph._element.set(qn('w:bidi'), '1')
                            except:
                                pass
                        else:
                            # اتجاه من اليسار لليمين للنص الإنجليزي
                            new_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

                        # نسخ التنسيق إذا أمكن
                        try:
                            new_paragraph.style = paragraph.style
                        except:
                            pass
                    else:
                        # في حالة فشل الترجمة، أضف النص الأصلي
                        new_doc.add_paragraph(paragraph.text)
                else:
                    # فقرة فارغة
                    new_doc.add_paragraph("")
            
            # معالجة الجداول
            for table in doc.tables:
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))
                
                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        if cell.text.strip():
                            # ترجمة نص الخلية
                            success, translated_text = translation_function(cell.text)
                            new_table.cell(i, j).text = translated_text if success else cell.text
                        else:
                            new_table.cell(i, j).text = ""
            
            # حفظ المستند الجديد
            output_buffer = io.BytesIO()
            new_doc.save(output_buffer)
            output_buffer.seek(0)
            
            return True, output_buffer.getvalue()
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف Word: {str(e)}"
    
    def extract_images_from_pdf(self, file_data):
        """استخراج الصور من ملف PDF (للحفاظ عليها دون ترجمة)"""
        try:
            images = []
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
            
            for page_num, page in enumerate(pdf_reader.pages):
                if '/XObject' in page['/Resources']:
                    xObject = page['/Resources']['/XObject'].get_object()
                    
                    for obj in xObject:
                        if xObject[obj]['/Subtype'] == '/Image':
                            # استخراج الصورة
                            try:
                                image_data = xObject[obj].get_data()
                                images.append({
                                    'page': page_num + 1,
                                    'data': image_data,
                                    'name': f"image_page_{page_num + 1}_{obj}"
                                })
                            except:
                                continue
            
            return images
            
        except Exception as e:
            return []
    
    def create_translation_progress_file(self, file_name, total_pages):
        """إنشاء ملف لتتبع تقدم الترجمة"""
        progress_data = {
            'file_name': file_name,
            'total_pages': total_pages,
            'completed_pages': 0,
            'status': 'in_progress',
            'created_at': str(os.time.time())
        }
        
        progress_file = os.path.join(self.temp_dir, f"progress_{file_name}.json")
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
        
        return progress_file
    
    def update_translation_progress(self, progress_file, completed_pages):
        """تحديث تقدم الترجمة"""
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            progress_data['completed_pages'] = completed_pages
            progress_data['status'] = 'completed' if completed_pages >= progress_data['total_pages'] else 'in_progress'
            
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            return True
        except:
            return False
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = tempfile.mkdtemp()
        except:
            pass
    
    def get_file_page_count(self, file_data, file_name):
        """الحصول على عدد صفحات الملف"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        try:
            if file_extension == '.pdf':
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
                return len(pdf_reader.pages)
            elif file_extension == '.docx':
                # تقدير عدد الصفحات بناءً على عدد الفقرات
                doc = Document(io.BytesIO(file_data))
                paragraph_count = len([p for p in doc.paragraphs if p.text.strip()])
                estimated_pages = max(1, paragraph_count // 20)  # تقدير 20 فقرة لكل صفحة
                return estimated_pages
            else:
                return 1
        except:
            return 1

# إنشاء مثيل عام للاستخدام
file_processor = FileProcessor()
