"""
نظام معالجة الملفات المتقدم
يدعم معالجة الملفات صفحة بصفحة مع الحفاظ على التنسيق
"""

import os
import tempfile
import io
import json
from docx import Document
from docx.shared import Inches
import PyPDF2
import pdfplumber
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.utils import ImageReader
from PIL import Image
import sqlite3

class FileProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_arabic_fonts()
    
    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية للـ PDF"""
        try:
            import platform
            system = platform.system().lower()

            # مسارات الخطوط حسب نظام التشغيل
            if system == 'windows':
                arabic_font_paths = [
                    r'C:\Windows\Fonts\arial.ttf',
                    r'C:\Windows\Fonts\calibri.ttf',
                    r'C:\Windows\Fonts\tahoma.ttf'
                ]
            elif system == 'darwin':  # macOS
                arabic_font_paths = [
                    '/System/Library/Fonts/Arial.ttf',
                    '/System/Library/Fonts/Helvetica.ttc',
                    '/Library/Fonts/Arial.ttf'
                ]
            else:  # Linux
                arabic_font_paths = [
                    '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                    '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
                    '/usr/share/fonts/TTF/arial.ttf',
                    '/usr/share/fonts/truetype/noto/NotoSans-Regular.ttf'
                ]

            # محاولة تسجيل خط مناسب
            for font_path in arabic_font_paths:
                if os.path.exists(font_path):
                    try:
                        # تسجيل الخط مع معالجة أفضل للأخطاء
                        pdfmetrics.registerFont(TTFont('Arabic', font_path, validate=True))
                        self.arabic_font = 'Arabic'
                        return
                    except Exception as font_error:
                        # تجاهل أخطاء الخطوط المعطوبة والمتابعة
                        continue

            # إذا لم يتم العثور على خط مناسب، استخدم الخط الافتراضي
            self.arabic_font = 'Helvetica'

        except Exception as e:
            # في حالة أي خطأ، استخدم الخط الافتراضي
            self.arabic_font = 'Helvetica'
    
    def process_file_by_pages(self, file_data, file_name, translation_function):
        """معالجة الملف صفحة بصفحة مع الترجمة"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        if file_extension == '.pdf':
            return self.process_pdf_by_pages(file_data, file_name, translation_function)
        elif file_extension == '.docx':
            return self.process_docx_by_pages(file_data, file_name, translation_function)
        else:
            return False, "نوع الملف غير مدعوم للمعالجة المتقدمة"
    
    def process_pdf_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف PDF صفحة بصفحة مع الحفاظ على التنسيق"""
        try:
            # قراءة PDF الأصلي
            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                total_pages = len(pdf.pages)

                # إنشاء PDF جديد للترجمة
                output_buffer = io.BytesIO()
                c = canvas.Canvas(output_buffer, pagesize=A4)

                for page_num, page in enumerate(pdf.pages):
                    # استخراج النص من الصفحة مع الحفاظ على التخطيط
                    page_text = page.extract_text()

                    if page_text and page_text.strip():
                        # ترجمة النص
                        success, translated_text = translation_function(page_text)

                        if success:
                            # إضافة الصفحة المترجمة مع تحسين التنسيق
                            self.add_formatted_text_to_pdf_page(c, translated_text, page_text, page_num + 1)
                        else:
                            # في حالة فشل الترجمة، أضف النص الأصلي
                            self.add_formatted_text_to_pdf_page(c, page_text, page_text, page_num + 1)
                    else:
                        # صفحة فارغة أو تحتوي على صور فقط
                        self.add_text_to_pdf_page(c, f"صفحة {page_num + 1} - لا يوجد نص للترجمة", page_num + 1)

                    # إنهاء الصفحة
                    c.showPage()

                # حفظ PDF
                c.save()
                output_buffer.seek(0)

                return True, output_buffer.getvalue()

        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF: {str(e)}"
    
    def add_text_to_pdf_page(self, canvas_obj, text, page_num):
        """إضافة نص إلى صفحة PDF مع معالجة محسنة للأخطاء"""
        try:
            # محاولة إعداد الخط مع معالجة الأخطاء
            try:
                canvas_obj.setFont(self.arabic_font, 12)
            except Exception as font_error:
                # إذا فشل الخط المخصص، استخدم الخط الافتراضي
                canvas_obj.setFont('Helvetica', 12)
                self.arabic_font = 'Helvetica'

            # تنظيف النص من الأحرف المشكلة
            clean_text = self.clean_text_for_pdf(text)

            # تقسيم النص إلى أسطر
            lines = self.wrap_text(clean_text, 70)  # 70 حرف لكل سطر

            # بدء الكتابة من أعلى الصفحة
            y_position = 750

            # إضافة رقم الصفحة
            try:
                canvas_obj.drawString(50, 800, f"Page {page_num}")
            except:
                canvas_obj.drawString(50, 800, f"Page {page_num}".encode('ascii', 'ignore').decode())

            # إضافة النص سطر بسطر
            for line in lines:
                if y_position < 50:  # إذا وصلنا لنهاية الصفحة
                    break

                try:
                    canvas_obj.drawString(50, y_position, line)
                except Exception as line_error:
                    # في حالة فشل كتابة السطر، حاول مع تنظيف إضافي
                    try:
                        safe_line = line.encode('ascii', 'ignore').decode()
                        canvas_obj.drawString(50, y_position, safe_line)
                    except:
                        # إذا فشل كل شيء، اكتب رسالة بديلة
                        canvas_obj.drawString(50, y_position, "[Text encoding error]")

                y_position -= 15  # المسافة بين الأسطر

        except Exception as e:
            # في حالة الخطأ العام، أضف نص بسيط
            try:
                canvas_obj.setFont('Helvetica', 10)
                canvas_obj.drawString(50, 400, f"Error displaying text on page {page_num}")
                canvas_obj.drawString(50, 380, "Text content could not be rendered properly")
            except:
                pass  # تجاهل الأخطاء في حالة الطوارئ

    def clean_text_for_pdf(self, text):
        """تنظيف النص للتأكد من توافقه مع PDF مع إصلاح الأحرف العربية"""
        try:
            # إزالة الأحرف غير المطبوعة والمشكلة
            import re

            # إزالة الأحرف التحكمية
            text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

            # استبدال الأحرف المشكلة
            text = text.replace('\u200e', '')  # Left-to-right mark
            text = text.replace('\u200f', '')  # Right-to-left mark
            text = text.replace('\ufeff', '')  # Byte order mark

            # إصلاح اتجاه النص العربي
            text = self.fix_arabic_text_direction(text)

            # التأكد من أن النص قابل للترميز
            text = text.encode('utf-8', 'ignore').decode('utf-8')

            return text

        except Exception as e:
            # في حالة فشل التنظيف، أرجع نص آمن
            return str(text).encode('ascii', 'ignore').decode()

    def fix_arabic_text_direction(self, text):
        """إصلاح اتجاه النص العربي المعكوس"""
        try:
            # استخدام مكتبة python-bidi لإصلاح اتجاه النص
            try:
                from bidi.algorithm import get_display
                # إصلاح اتجاه النص العربي
                text = get_display(text)
            except ImportError:
                # إذا لم تكن مكتبة bidi متوفرة، استخدم حل بديل
                text = self.fix_arabic_manual(text)

            return text

        except Exception as e:
            return text

    def fix_arabic_manual(self, text):
        """إصلاح اتجاه النص العربي يدوياً"""
        try:
            import re

            # تحديد النص العربي
            arabic_pattern = r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+'

            def reverse_arabic_words(match):
                arabic_text = match.group()
                # عكس ترتيب الكلمات العربية فقط
                words = arabic_text.split()
                return ' '.join(reversed(words))

            # تطبيق الإصلاح على النص العربي فقط
            fixed_text = re.sub(arabic_pattern, reverse_arabic_words, text)

            return fixed_text

        except Exception as e:
            return text

    def add_formatted_text_to_pdf_page(self, canvas_obj, translated_text, original_text, page_num):
        """إضافة نص منسق إلى صفحة PDF مع الحفاظ على التخطيط"""
        try:
            # محاولة إعداد الخط مع معالجة الأخطاء
            try:
                canvas_obj.setFont(self.arabic_font, 12)
            except Exception as font_error:
                canvas_obj.setFont('Helvetica', 12)
                self.arabic_font = 'Helvetica'

            # تنظيف النص المترجم
            clean_translated = self.clean_text_for_pdf(translated_text)
            clean_original = self.clean_text_for_pdf(original_text)

            # إنشاء تخطيط مختلط (أصلي ومترجم)
            mixed_content = self.create_mixed_layout(clean_original, clean_translated)

            # تقسيم النص إلى أسطر
            lines = self.wrap_text(mixed_content, 70)

            # بدء الكتابة من أعلى الصفحة
            y_position = 750

            # إضافة رقم الصفحة والعنوان
            try:
                canvas_obj.drawString(50, 800, f"Page {page_num} - Translated Document")
            except:
                canvas_obj.drawString(50, 800, f"Page {page_num}")

            # إضافة النص سطر بسطر
            for line in lines:
                if y_position < 50:  # إذا وصلنا لنهاية الصفحة
                    break

                try:
                    canvas_obj.drawString(50, y_position, line)
                except Exception as line_error:
                    try:
                        safe_line = line.encode('ascii', 'ignore').decode()
                        canvas_obj.drawString(50, y_position, safe_line)
                    except:
                        canvas_obj.drawString(50, y_position, "[Text encoding error]")

                y_position -= 15

        except Exception as e:
            # في حالة الخطأ، استخدم الطريقة البسيطة
            self.add_text_to_pdf_page(canvas_obj, translated_text, page_num)

    def create_mixed_layout(self, original_text, translated_text):
        """إنشاء تخطيط مختلط للنص الأصلي والمترجم"""
        try:
            # تقسيم النصوص إلى فقرات
            original_paragraphs = original_text.split('\n\n')
            translated_paragraphs = translated_text.split('\n\n')

            mixed_content = []

            # دمج الفقرات الأصلية والمترجمة
            max_paragraphs = max(len(original_paragraphs), len(translated_paragraphs))

            for i in range(max_paragraphs):
                if i < len(original_paragraphs):
                    original_para = original_paragraphs[i].strip()
                    if original_para:
                        mixed_content.append(f"[ORIGINAL] {original_para}")

                if i < len(translated_paragraphs):
                    translated_para = translated_paragraphs[i].strip()
                    if translated_para:
                        mixed_content.append(f"[TRANSLATED] {translated_para}")

                mixed_content.append("")  # فاصل بين الفقرات

            return "\n".join(mixed_content)

        except Exception as e:
            # في حالة الخطأ، أرجع النص المترجم فقط
            return translated_text
    
    def wrap_text(self, text, width):
        """تقسيم النص إلى أسطر بعرض محدد"""
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            if len(current_line + " " + word) <= width:
                current_line += " " + word if current_line else word
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def process_docx_by_pages(self, file_data, file_name, translation_function):
        """معالجة ملف Word صفحة بصفحة"""
        try:
            # قراءة المستند الأصلي
            doc = Document(io.BytesIO(file_data))
            
            # إنشاء مستند جديد للترجمة
            new_doc = Document()
            
            # معالجة الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # ترجمة النص
                    success, translated_text = translation_function(paragraph.text)
                    
                    if success:
                        # إضافة الفقرة المترجمة
                        new_paragraph = new_doc.add_paragraph(translated_text)
                        # نسخ التنسيق إذا أمكن
                        try:
                            new_paragraph.style = paragraph.style
                        except:
                            pass
                    else:
                        # في حالة فشل الترجمة، أضف النص الأصلي
                        new_doc.add_paragraph(paragraph.text)
                else:
                    # فقرة فارغة
                    new_doc.add_paragraph("")
            
            # معالجة الجداول
            for table in doc.tables:
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))
                
                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        if cell.text.strip():
                            # ترجمة نص الخلية
                            success, translated_text = translation_function(cell.text)
                            new_table.cell(i, j).text = translated_text if success else cell.text
                        else:
                            new_table.cell(i, j).text = ""
            
            # حفظ المستند الجديد
            output_buffer = io.BytesIO()
            new_doc.save(output_buffer)
            output_buffer.seek(0)
            
            return True, output_buffer.getvalue()
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف Word: {str(e)}"
    
    def extract_images_from_pdf(self, file_data):
        """استخراج الصور من ملف PDF (للحفاظ عليها دون ترجمة)"""
        try:
            images = []
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
            
            for page_num, page in enumerate(pdf_reader.pages):
                if '/XObject' in page['/Resources']:
                    xObject = page['/Resources']['/XObject'].get_object()
                    
                    for obj in xObject:
                        if xObject[obj]['/Subtype'] == '/Image':
                            # استخراج الصورة
                            try:
                                image_data = xObject[obj].get_data()
                                images.append({
                                    'page': page_num + 1,
                                    'data': image_data,
                                    'name': f"image_page_{page_num + 1}_{obj}"
                                })
                            except:
                                continue
            
            return images
            
        except Exception as e:
            return []
    
    def create_translation_progress_file(self, file_name, total_pages):
        """إنشاء ملف لتتبع تقدم الترجمة"""
        progress_data = {
            'file_name': file_name,
            'total_pages': total_pages,
            'completed_pages': 0,
            'status': 'in_progress',
            'created_at': str(os.time.time())
        }
        
        progress_file = os.path.join(self.temp_dir, f"progress_{file_name}.json")
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
        
        return progress_file
    
    def update_translation_progress(self, progress_file, completed_pages):
        """تحديث تقدم الترجمة"""
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            progress_data['completed_pages'] = completed_pages
            progress_data['status'] = 'completed' if completed_pages >= progress_data['total_pages'] else 'in_progress'
            
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            return True
        except:
            return False
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = tempfile.mkdtemp()
        except:
            pass
    
    def get_file_page_count(self, file_data, file_name):
        """الحصول على عدد صفحات الملف"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        try:
            if file_extension == '.pdf':
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
                return len(pdf_reader.pages)
            elif file_extension == '.docx':
                # تقدير عدد الصفحات بناءً على عدد الفقرات
                doc = Document(io.BytesIO(file_data))
                paragraph_count = len([p for p in doc.paragraphs if p.text.strip()])
                estimated_pages = max(1, paragraph_count // 20)  # تقدير 20 فقرة لكل صفحة
                return estimated_pages
            else:
                return 1
        except:
            return 1

# إنشاء مثيل عام للاستخدام
file_processor = FileProcessor()
