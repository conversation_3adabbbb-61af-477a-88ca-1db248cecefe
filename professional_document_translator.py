"""
نظام ترجمة المستندات الاحترافي
مصمم خصيصاً لبيئة Linux Server بدون واجهة رسومية
يدعم PDF و Word مع الحفاظ على التنسيق والصور
"""

import os
import io
import tempfile
import logging
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
import fitz  # PyMuPDF
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from pdf2docx import Converter
import arabic_reshaper
from bidi.algorithm import get_display
from deep_translator import GoogleTranslator
import cv2
import numpy as np
from PIL import Image

# إعداد التسجيل - فقط للملف، بدون التيرمنال
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)8s] %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler('pdf_translation.log', encoding='utf-8')
        # تم حذف StreamHandler لإخفاء الرسائل من التيرمنال
    ]
)
logger = logging.getLogger(__name__)

class ProfessionalDocumentTranslator:
    """
    نظام ترجمة المستندات الاحترافي
    مصمم للعمل على Linux Server بدون مشاكل
    """
    
    def __init__(self):
        """تهيئة النظام"""
        self.temp_dir = tempfile.mkdtemp()
        self.supported_formats = {
            'pdf': ['.pdf'],
            'word': ['.docx', '.doc']
        }
        self.setup_arabic_support()
        # تم تهيئة نظام الترجمة الاحترافي
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        try:
            # اختبار arabic_reshaper
            test_text = "مرحبا"
            self.arabic_reshaper_available = True
            arabic_reshaper.reshape(test_text)
            
            # اختبار python-bidi
            self.bidi_available = True
            get_display(test_text)
            
            # تم تسجيل العملية
        except Exception as e:
            # تحذير
            pass
            self.arabic_reshaper_available = False
            self.bidi_available = False
    
    def detect_language(self, text: str) -> str:
        """كشف لغة النص"""
        if not text or not text.strip():
            return 'unknown'
        
        # عد الأحرف العربية
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = sum(1 for char in text if char.isalpha())
        
        if total_chars == 0:
            return 'unknown'
        
        arabic_ratio = arabic_chars / total_chars
        return 'arabic' if arabic_ratio > 0.3 else 'english'
    
    def fix_arabic_text(self, text: str) -> str:
        """إصلاح النص العربي للعرض الصحيح"""
        try:
            if not text or not text.strip():
                return text
            
            # تنظيف النص
            cleaned_text = self.clean_text(text)
            
            # إعادة تشكيل الأحرف العربية
            if self.arabic_reshaper_available:
                reshaped_text = arabic_reshaper.reshape(cleaned_text)
            else:
                reshaped_text = cleaned_text
            
            # إصلاح اتجاه النص
            if self.bidi_available:
                final_text = get_display(reshaped_text)
            else:
                final_text = reshaped_text
            
            return final_text
        except Exception as e:
            # خطأ في إصلاح النص العربي
            return text
    
    def clean_text(self, text: str) -> str:
        """تنظيف النص من الأحرف المشكلة"""
        import re
        # إزالة الأحرف التحكمية
        text = re.sub(r'[\u200e\u200f\u202a\u202b\u202c\u202d\u202e]', '', text)
        # إزالة Zero Width characters
        text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)
        # تنظيف المسافات
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def translate_text(self, text: str, target_lang: str = 'ar') -> str:
        """ترجمة النص باستخدام Google Translate"""
        try:
            if not text or not text.strip():
                return text
            
            # تحديد اللغة المصدر
            source_lang = 'auto'
            
            # إنشاء المترجم
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            
            # ترجمة النص
            translated = translator.translate(text)
            
            # إصلاح النص العربي إذا كانت الترجمة للعربية
            if target_lang == 'ar':
                translated = self.fix_arabic_text(translated)
            
            return translated
        except Exception as e:
            # خطأ في العملية
            pass
            return text
    
    def process_pdf_advanced(self, pdf_data: bytes, target_lang: str = 'ar', progress_callback=None) -> bytes:
        """معالجة PDF متقدمة مع الحفاظ على التنسيق والصور"""
        try:
            # فتح PDF
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            total_pages = len(doc)

            if progress_callback:
                progress_callback(0, f"📄 بدء معالجة PDF ({total_pages} صفحة)")

            # معالجة كل صفحة
            for page_num in range(total_pages):
                page = doc[page_num]

                # تحديث التقدم
                progress_percent = int((page_num / total_pages) * 100)
                if progress_callback:
                    progress_callback(progress_percent, f"🔄 معالجة الصفحة {page_num + 1}/{total_pages} ({progress_percent}%)")

                # استخراج النصوص مع المواقع
                text_dict = page.get_text("dict")
                
                # معالجة كل كتلة نص
                for block in text_dict["blocks"]:
                    if "lines" in block:  # كتلة نص
                        for line in block["lines"]:
                            for span in line["spans"]:
                                original_text = span["text"]
                                
                                if original_text.strip():
                                    # ترجمة النص
                                    translated_text = self.translate_text(original_text, target_lang)
                                    
                                    # الحصول على معلومات التنسيق
                                    bbox = span["bbox"]
                                    font_size = span["size"]
                                    font_name = span["font"]
                                    flags = span.get("flags", 0)
                                    
                                    # حذف النص الأصلي
                                    rect = fitz.Rect(bbox)
                                    page.add_redact_annot(rect)
                                    
                                    # إضافة النص المترجم
                                    self.insert_text_with_formatting(
                                        page, translated_text, bbox, font_size, 
                                        font_name, flags, target_lang
                                    )
                
                # تطبيق التغييرات
                page.apply_redactions()

            # تحديث التقدم - الانتهاء
            if progress_callback:
                progress_callback(100, "✅ تم الانتهاء من الترجمة - جاري حفظ الملف...")

            # حفظ PDF المعدل
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            doc.close()

            output_buffer.seek(0)
            return output_buffer.getvalue()
            
        except Exception as e:
            # خطأ في العملية
            pass
            raise
    
    def insert_text_with_formatting(self, page, text: str, bbox: tuple, 
                                   font_size: float, font_name: str, 
                                   flags: int, target_lang: str):
        """إدراج النص مع التنسيق المناسب"""
        try:
            # تحديد موقع النص
            x, y = bbox[0], bbox[1] + font_size * 0.8
            
            # تحديد المحاذاة حسب اللغة
            if target_lang == 'ar':
                # للعربية - محاذاة يمين
                x = bbox[2]  # البداية من اليمين
            
            # إدراج النص (بدون معامل flags للتوافق)
            page.insert_text(
                (x, y),
                text,
                fontsize=font_size,
                color=(0, 0, 0)
            )
            
        except Exception as e:
            # خطأ في إدراج النص
            pass
    
    def process_word_advanced(self, docx_data: bytes, target_lang: str = 'ar', progress_callback=None) -> bytes:
        """معالجة Word متقدمة مع الحفاظ على التنسيق"""
        try:
            # فتح المستند
            doc = Document(io.BytesIO(docx_data))

            # حساب إجمالي العناصر للمعالجة
            total_paragraphs = len([p for p in doc.paragraphs if p.text.strip()])
            total_table_cells = sum(len([cell for row in table.rows for cell in row.cells
                                       for p in cell.paragraphs if p.text.strip()])
                                  for table in doc.tables)
            total_elements = total_paragraphs + total_table_cells
            processed_elements = 0

            if progress_callback:
                progress_callback(0, f"📝 بدء معالجة Word ({total_elements} عنصر)")

            # معالجة الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    processed_elements += 1
                    progress_percent = int((processed_elements / total_elements) * 80)  # 80% للفقرات
                    if progress_callback:
                        progress_callback(progress_percent, f"🔄 ترجمة الفقرة {processed_elements}/{total_elements} ({progress_percent}%)")

                    self.translate_paragraph(paragraph, target_lang)
            
            # معالجة الجداول
            if progress_callback:
                progress_callback(80, "📊 معالجة الجداول...")

            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                processed_elements += 1
                                progress_percent = 80 + int(((processed_elements - total_paragraphs) / total_table_cells) * 15)  # 15% للجداول
                                if progress_callback:
                                    progress_callback(progress_percent, f"🔄 ترجمة جدول {processed_elements}/{total_elements} ({progress_percent}%)")

                                self.translate_paragraph(paragraph, target_lang)
            
            # معالجة الرؤوس والتذييلات
            for section in doc.sections:
                # الرأس
                if section.header:
                    for paragraph in section.header.paragraphs:
                        if paragraph.text.strip():
                            self.translate_paragraph(paragraph, target_lang)
                
                # التذييل
                if section.footer:
                    for paragraph in section.footer.paragraphs:
                        if paragraph.text.strip():
                            self.translate_paragraph(paragraph, target_lang)
            
            # تحديث التقدم - الانتهاء
            if progress_callback:
                progress_callback(100, "✅ تم الانتهاء من الترجمة - جاري حفظ الملف...")

            # حفظ المستند
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            output_buffer.seek(0)

            return output_buffer.getvalue()
            
        except Exception as e:
            # خطأ في العملية
            pass
            raise
    
    def translate_paragraph(self, paragraph, target_lang: str):
        """ترجمة فقرة مع الحفاظ على التنسيق"""
        try:
            original_text = paragraph.text
            
            # ترجمة النص
            translated_text = self.translate_text(original_text, target_lang)
            
            # حفظ التنسيق الأصلي
            original_runs = list(paragraph.runs)
            original_alignment = paragraph.alignment
            
            # مسح النص الحالي
            paragraph.clear()
            
            # إضافة النص المترجم
            new_run = paragraph.add_run(translated_text)
            
            # نسخ التنسيق من أول run
            if original_runs:
                first_run = original_runs[0]
                if first_run.font.name:
                    new_run.font.name = first_run.font.name
                if first_run.font.size:
                    new_run.font.size = first_run.font.size
                new_run.bold = first_run.bold
                new_run.italic = first_run.italic
                new_run.underline = first_run.underline
            
            # تحديد اتجاه النص
            if target_lang == 'ar':
                # للعربية - RTL
                paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                try:
                    paragraph._element.set(qn('w:bidi'), '1')
                except:
                    pass
            else:
                # للإنجليزية - LTR
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
                
        except Exception as e:
            # خطأ في العملية
            pass
    
    def convert_pdf_to_word(self, pdf_data: bytes) -> bytes:
        """تحويل PDF إلى Word باستخدام pdf2docx"""
        try:
            # حفظ PDF مؤقتاً
            pdf_path = os.path.join(self.temp_dir, "temp.pdf")
            docx_path = os.path.join(self.temp_dir, "temp.docx")
            
            with open(pdf_path, 'wb') as f:
                f.write(pdf_data)
            
            # التحويل
            cv = Converter(pdf_path)
            cv.convert(docx_path, start=0, end=None)
            cv.close()
            
            # قراءة النتيجة
            with open(docx_path, 'rb') as f:
                docx_data = f.read()
            
            # تنظيف الملفات المؤقتة
            os.remove(pdf_path)
            os.remove(docx_path)
            
            return docx_data
            
        except Exception as e:
            # خطأ في العملية
            pass
            raise
    
    def convert_word_to_pdf(self, docx_data: bytes) -> bytes:
        """تحويل Word إلى PDF"""
        try:
            # هذه الطريقة تتطلب LibreOffice على Linux
            # سنستخدم طريقة بديلة باستخدام python-docx و reportlab
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
            
            # قراءة المستند
            doc = Document(io.BytesIO(docx_data))
            
            # إنشاء PDF
            output_buffer = io.BytesIO()
            pdf_doc = SimpleDocTemplate(output_buffer, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # إضافة المحتوى
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # تحديد النمط حسب اللغة
                    lang = self.detect_language(paragraph.text)
                    if lang == 'arabic':
                        # للعربية
                        p = Paragraph(paragraph.text, styles['Normal'])
                    else:
                        # للإنجليزية
                        p = Paragraph(paragraph.text, styles['Normal'])
                    
                    story.append(p)
                    story.append(Spacer(1, 12))
            
            # بناء PDF
            pdf_doc.build(story)
            output_buffer.seek(0)
            
            return output_buffer.getvalue()
            
        except Exception as e:
            # خطأ في العملية
            pass
            raise

    def translate_document(self, file_data: bytes, file_name: str,
                          target_lang: str = 'ar', progress_callback=None) -> Tuple[bool, bytes, str]:
        """
        ترجمة المستند الرئيسية
        Args:
            file_data: بيانات الملف
            file_name: اسم الملف
            target_lang: اللغة المستهدفة ('ar' أو 'en')
        Returns:
            (success, translated_data, message)
        """
        try:
            file_ext = Path(file_name).suffix.lower()

            if file_ext == '.pdf':
                # معالجة PDF
                translated_data = self.process_pdf_advanced(file_data, target_lang, progress_callback)
                return True, translated_data, "تم ترجمة PDF بنجاح"

            elif file_ext in ['.docx', '.doc']:
                # معالجة Word
                if file_ext == '.doc':
                    # تحويل .doc إلى .docx أولاً (يتطلب LibreOffice على Linux)
                    # تحذير: "ملفات .doc تتطلب تحويل إلى .docx"
                    return False, b"", "ملفات .doc غير مدعومة مباشرة. يرجى استخدام .docx"

                translated_data = self.process_word_advanced(file_data, target_lang, progress_callback)
                return True, translated_data, "تم ترجمة Word بنجاح"

            else:
                return False, b"", f"نوع الملف غير مدعوم: {file_ext}"

        except Exception as e:
            # خطأ في العملية
            pass
            return False, b"", f"خطأ في الترجمة: {str(e)}"

    def get_document_info(self, file_data: bytes, file_name: str) -> Dict[str, Any]:
        """الحصول على معلومات المستند"""
        try:
            file_ext = Path(file_name).suffix.lower()
            info = {
                'file_name': file_name,
                'file_type': file_ext,
                'file_size': len(file_data),
                'pages': 0,
                'text_length': 0,
                'has_images': False,
                'has_tables': False
            }

            if file_ext == '.pdf':
                doc = fitz.open(stream=file_data, filetype="pdf")
                info['pages'] = len(doc)

                total_text = ""
                for page in doc:
                    text = page.get_text()
                    total_text += text

                    # فحص الصور
                    if page.get_images():
                        info['has_images'] = True

                info['text_length'] = len(total_text)
                doc.close()

            elif file_ext == '.docx':
                doc = Document(io.BytesIO(file_data))

                # عد الفقرات
                total_text = ""
                for paragraph in doc.paragraphs:
                    total_text += paragraph.text

                info['text_length'] = len(total_text)
                info['pages'] = max(1, len(doc.paragraphs) // 20)  # تقدير

                # فحص الجداول
                if doc.tables:
                    info['has_tables'] = True

                # فحص الصور (تقريبي)
                for rel in doc.part.rels.values():
                    if "image" in rel.target_ref:
                        info['has_images'] = True
                        break

            return info

        except Exception as e:
            # خطأ في العملية
            pass
            return {'error': str(e)}

    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            # تم تسجيل العملية
        except Exception as e:
            # خطأ في العملية
            pass

    def __del__(self):
        """تنظيف تلقائي عند حذف الكائن"""
        self.cleanup()

# إنشاء مثيل عام للاستخدام
professional_translator = ProfessionalDocumentTranslator()

def translate_document_file(file_data: bytes, file_name: str,
                          target_lang: str = 'ar', progress_callback=None) -> Tuple[bool, bytes, str]:
    """دالة مساعدة للترجمة السريعة"""
    return professional_translator.translate_document(file_data, file_name, target_lang, progress_callback)

def get_file_info(file_data: bytes, file_name: str) -> Dict[str, Any]:
    """دالة مساعدة للحصول على معلومات الملف"""
    return professional_translator.get_document_info(file_data, file_name)
