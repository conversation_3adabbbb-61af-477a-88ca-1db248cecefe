# دليل إعداد نظام الترجمة المتكامل

## متطلبات النظام

### 1. تثبيت المكتبات الأساسية
```bash
pip install -r requirements.txt
```

### 2. تثبيت Tesseract OCR على Linux
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-ara  # للغة العربية
sudo apt-get install tesseract-ocr-eng  # للغة الإنجليزية
sudo apt-get install libtesseract-dev

# CentOS/RHEL/Fedora
sudo yum install tesseract
sudo yum install tesseract-langpack-ara
sudo yum install tesseract-langpack-eng

# أو باستخدام dnf
sudo dnf install tesseract
sudo dnf install tesseract-langpack-ara
sudo dnf install tesseract-langpack-eng
```

### 3. تثبيت مكتبات إضافية لمعالجة PDF
```bash
sudo apt-get install poppler-utils  # لمعالجة PDF إضافية
```

### 4. التحقق من التثبيت
```bash
tesseract --version
tesseract --list-langs  # يجب أن تظهر ara و eng
```

## إعداد البوت

### 1. إنشاء أزرار الترجمة
بعد تشغيل البوت، يمكن للأدمن إنشاء أزرار الترجمة:

1. اضغط على "END | +"
2. أدخل اسم الزر (مثل: "ترجمة إلى العربية")
3. اختر نوع الزر: "إلى العربية" أو "إلى الإنجليزية"
4. سيتم إنشاء الزر مع الإعدادات الافتراضية

### 2. تخصيص إعدادات الترجمة
بعد إنشاء زر الترجمة، يمكن للأدمن تخصيص:

- **مفاتيح API**: لاستخدام الذكاء الاصطناعي المخصص
- **البرومبت**: تخصيص تعليمات الترجمة
- **النموذج**: اختيار نموذج الذكاء الاصطناعي
- **التكلفة**: تحديد تكلفة الترجمة بالنقاط

### 3. البرومبتات المخصصة
يتم تحميل البرومبتات من ملف `برومبتات الترجمة .txt`:

- **للترجمة إلى العربية**: برومبت متخصص للترجمة الطبية والعلمية
- **للترجمة إلى الإنجليزية**: برومبت متخصص للترجمة الأكاديمية

## الميزات المدعومة

### 1. ترجمة النصوص العادية
- إرسال نص مباشر للترجمة
- دعم النصوص الطويلة
- ترجمة فورية

### 2. ترجمة الصور
- استخراج النص من الصور باستخدام OCR
- دعم اللغات العربية والإنجليزية
- معالجة الصور تلقائياً لتحسين دقة OCR

### 3. ترجمة الملفات
**الملفات المدعومة:**
- PDF
- Word (.docx)
- ملفات نصية (.txt)
- صور (JPG, PNG, BMP, TIFF)

**المعالجة:**
- استخراج النص صفحة بصفحة
- الحفاظ على التنسيق الأصلي
- إنشاء ملف مترجم جديد

### 4. نظام النقاط
- تكلفة مختلفة لكل نوع ترجمة
- خصم تلقائي من رصيد المستخدم
- التبديل للترجمة العادية عند نفاد النقاط

### 5. نظام الحماية
- حماية من تسريب البرومبتات
- منع التلاعب بتعليمات النظام
- ترجمة فقط بدون محادثات

## إعدادات النظام

### إعدادات عامة (في قاعدة البيانات):
```sql
-- نوع الترجمة الافتراضي
translation_mode: 'ai' أو 'normal'

-- التبديل التلقائي للترجمة العادية
auto_fallback: '1' (مفعل) أو '0' (معطل)

-- الحد الأقصى لحجم الملف (بالبايت)
max_file_size: '10485760' (10MB)

-- صيغ الملفات المدعومة
supported_formats: 'pdf,docx,txt,jpg,jpeg,png'

-- لغات OCR
ocr_language: 'ara+eng'
```

### تكلفة النقاط:
- ترجمة النص بالذكاء الاصطناعي: 3 نقاط
- ترجمة النص العادية: 2 نقطة
- ترجمة الصور: 5 نقاط
- ترجمة الملفات: 10 نقاط

## استكشاف الأخطاء

### 1. خطأ Tesseract
```
Error: Tesseract not found
```
**الحل:**
```bash
sudo apt-get install tesseract-ocr
# تحديد مسار tesseract يدوياً في text_extraction.py
```

### 2. خطأ في قراءة PDF
```
Error: Failed to extract text from PDF
```
**الحل:**
- تأكد من أن الملف ليس محمياً بكلمة مرور
- تأكد من أن PDF يحتوي على نص وليس صور فقط

### 3. خطأ في معالجة الصور
```
Error: Failed to extract text from image
```
**الحل:**
- تأكد من وضوح النص في الصورة
- تأكد من تثبيت tesseract-ocr-ara للنصوص العربية

### 4. نفاد النقاط
```
عذراً، لقد نفدت نقاطك!
```
**الحل:**
- شراء نقاط إضافية
- أو استخدام الترجمة العادية (تكلفة أقل)

## الأمان والحماية

### 1. حماية البرومبتات
- تشفير تعليمات النظام
- منع تسريب المعلومات الحساسة
- رفض طلبات التلاعب

### 2. حماية الملفات
- فحص نوع وحجم الملفات
- تنظيف الملفات المؤقتة
- حماية من الملفات الضارة

### 3. إدارة الموارد
- حد أقصى لحجم الملفات
- تنظيف الذاكرة المؤقتة
- إدارة استهلاك النقاط

## الدعم الفني

### ملفات السجلات
- تحقق من سجلات البوت للأخطاء
- راجع استهلاك الذاكرة والمعالج
- تابع استخدام مساحة التخزين

### النسخ الاحتياطي
- نسخ احتياطي لقاعدة البيانات
- حفظ إعدادات الترجمة
- نسخ احتياطي للبرومبتات المخصصة

### التحديثات
- تحديث المكتبات بانتظام
- مراجعة أمان النظام
- تحسين أداء الترجمة
