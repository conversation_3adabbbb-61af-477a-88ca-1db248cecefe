#!/usr/bin/env python3
"""
اختبار للتأكد من عدم ظهور أي رسائل سجل في التيرمنال
"""

import os
import sys
import io
import contextlib

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def capture_terminal_output(func):
    """التقاط مخرجات التيرمنال"""
    # التقاط stdout و stderr
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    
    stdout_capture = io.StringIO()
    stderr_capture = io.StringIO()
    
    try:
        sys.stdout = stdout_capture
        sys.stderr = stderr_capture
        
        # تشغيل الدالة
        result = func()
        
        # الحصول على المخرجات
        stdout_content = stdout_capture.getvalue()
        stderr_content = stderr_capture.getvalue()
        
        return result, stdout_content, stderr_content
        
    finally:
        # إعادة تعيين stdout و stderr
        sys.stdout = old_stdout
        sys.stderr = old_stderr

def test_ultimate_translator_logs():
    """اختبار عدم ظهور رسائل من ultimate_pdf_translator"""
    print("🔍 اختبار ultimate_pdf_translator...")
    
    def run_test():
        from ultimate_pdf_translator import ultimate_translator
        # اختبار ترجمة بسيطة
        test_text = "Hello World"
        translated = ultimate_translator._translate_text_cached(test_text, 'ar')
        return translated
    
    result, stdout, stderr = capture_terminal_output(run_test)
    
    # فحص المخرجات
    log_messages_found = []
    
    # البحث عن رسائل السجل في stdout
    if any(keyword in stdout for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR', 'FONT_DOWNLOAD', 'SYSTEM_INIT']):
        log_messages_found.append("stdout")
    
    # البحث عن رسائل السجل في stderr
    if any(keyword in stderr for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR', 'FONT_DOWNLOAD', 'SYSTEM_INIT']):
        log_messages_found.append("stderr")
    
    if log_messages_found:
        print(f"   ❌ تم العثور على رسائل سجل في: {', '.join(log_messages_found)}")
        print(f"   📄 stdout: {stdout[:200]}...")
        print(f"   📄 stderr: {stderr[:200]}...")
        return False
    else:
        print(f"   ✅ لا توجد رسائل سجل في التيرمنال")
        print(f"   ✅ الترجمة تعمل: 'Hello World' → '{result}'")
        return True

def test_enhanced_translator_logs():
    """اختبار عدم ظهور رسائل من enhanced_pdf_translator"""
    print("\n🔍 اختبار enhanced_pdf_translator...")
    
    def run_test():
        from enhanced_pdf_translator import enhanced_pdf_translator
        # اختبار ترجمة بسيطة
        test_text = "Test message"
        translated = enhanced_pdf_translator.translate_text_cached(test_text, 'ar')
        return translated
    
    result, stdout, stderr = capture_terminal_output(run_test)
    
    # فحص المخرجات
    log_messages_found = []
    
    # البحث عن رسائل السجل
    if any(keyword in stdout for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR', 'لم يتم العثور على خطوط عربية']):
        log_messages_found.append("stdout")
    
    if any(keyword in stderr for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR', 'لم يتم العثور على خطوط عربية']):
        log_messages_found.append("stderr")
    
    if log_messages_found:
        print(f"   ❌ تم العثور على رسائل سجل في: {', '.join(log_messages_found)}")
        print(f"   📄 stdout: {stdout[:200]}...")
        print(f"   📄 stderr: {stderr[:200]}...")
        return False
    else:
        print(f"   ✅ لا توجد رسائل سجل في التيرمنال")
        print(f"   ✅ الترجمة تعمل: 'Test message' → '{result}'")
        return True

def test_professional_translator_logs():
    """اختبار عدم ظهور رسائل من professional_document_translator"""
    print("\n🔍 اختبار professional_document_translator...")
    
    def run_test():
        from professional_document_translator import professional_translator
        # اختبار بسيط
        return "تم الاختبار"
    
    result, stdout, stderr = capture_terminal_output(run_test)
    
    # فحص المخرجات
    log_messages_found = []
    
    # البحث عن رسائل السجل
    if any(keyword in stdout for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR']):
        log_messages_found.append("stdout")
    
    if any(keyword in stderr for keyword in ['INFO', 'DEBUG', 'WARNING', 'ERROR']):
        log_messages_found.append("stderr")
    
    if log_messages_found:
        print(f"   ❌ تم العثور على رسائل سجل في: {', '.join(log_messages_found)}")
        return False
    else:
        print(f"   ✅ لا توجد رسائل سجل في التيرمنال")
        return True

def test_log_file_creation():
    """اختبار إنشاء ملف السجل"""
    print("\n🔍 اختبار إنشاء ملف السجل...")
    
    log_file = "pdf_translation.log"
    
    # التحقق من وجود ملف السجل
    if os.path.exists(log_file):
        print(f"   ✅ ملف السجل موجود: {log_file}")
        
        # قراءة آخر بضعة أسطر
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"   📊 عدد أسطر السجل: {len(lines)}")
                    print(f"   📝 آخر سطر: {lines[-1].strip()}")
                else:
                    print("   📝 ملف السجل فارغ")
        except Exception as e:
            print(f"   ⚠️ خطأ في قراءة ملف السجل: {e}")
        
        return True
    else:
        print(f"   ❌ ملف السجل غير موجود: {log_file}")
        return False

def main():
    """تشغيل جميع اختبارات إخفاء رسائل السجل"""
    print("🔇 بدء اختبار إخفاء رسائل السجل من التيرمنال")
    print("=" * 60)
    
    tests = [
        ("ultimate_pdf_translator", test_ultimate_translator_logs),
        ("enhanced_pdf_translator", test_enhanced_translator_logs),
        ("professional_document_translator", test_professional_translator_logs),
        ("إنشاء ملف السجل", test_log_file_creation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"💥 {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج اختبار إخفاء رسائل السجل:")
    print(f"✅ نجح: {passed_tests}/{total_tests} اختبار")
    print(f"❌ فشل: {total_tests - passed_tests}/{total_tests} اختبار")
    print(f"📈 معدل النجاح: {(passed_tests / total_tests) * 100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إخفاء جميع رسائل السجل من التيرمنال بنجاح!")
        print("\n✨ النتائج المحققة:")
        print("   🔇 التيرمنال نظيف تماماً")
        print("   📋 السجل محفوظ في الملف")
        print("   🚀 النظام يعمل بصمت")
        
        print("\n💡 ملاحظة:")
        print("   📁 جميع رسائل السجل محفوظة في: pdf_translation.log")
        print("   🔍 يمكن مراجعة السجل للتشخيص عند الحاجة")
        
        return True
    else:
        print("\n⚠️ بعض الملفات ما زالت تظهر رسائل في التيرمنال")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
