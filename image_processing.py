"""
نظام معالجة الصور المتقدم
يدعم استخراج النص وترجمته وإعادة وضعه في الصورة
"""

import os
import io
import tempfile
from PIL import Image, ImageDraw, ImageFont
import pytesseract
import cv2
import numpy as np

class ImageProcessor:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط للكتابة على الصور"""
        # مسارات الخطوط المحتملة على Linux
        font_paths = [
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/TTF/arial.ttf',
            '/System/Library/Fonts/Arial.ttf',
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf'
        ]
        
        self.fonts = {}
        for size in [12, 14, 16, 18, 20, 24, 28, 32]:
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        self.fonts[size] = ImageFont.truetype(font_path, size)
                        break
                    except:
                        continue
            
            # إذا لم يتم العثور على خط، استخدم الخط الافتراضي
            if size not in self.fonts:
                try:
                    self.fonts[size] = ImageFont.load_default()
                except:
                    self.fonts[size] = None
    
    def extract_text_with_coordinates(self, image_data, languages='ara+eng'):
        """استخراج النص مع إحداثياته من الصورة"""
        try:
            # تحويل البيانات إلى صورة
            image = Image.open(io.BytesIO(image_data))
            
            # تحسين الصورة للـ OCR
            processed_image = self.preprocess_for_ocr(image)
            
            # استخراج النص مع الإحداثيات
            data = pytesseract.image_to_data(processed_image, lang=languages, output_type=pytesseract.Output.DICT)
            
            text_blocks = []
            n_boxes = len(data['text'])
            
            for i in range(n_boxes):
                text = data['text'][i].strip()
                if text:  # إذا كان هناك نص
                    confidence = int(data['conf'][i])
                    if confidence > 30:  # فقط النصوص ذات الثقة العالية
                        text_blocks.append({
                            'text': text,
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i],
                            'confidence': confidence
                        })
            
            return True, text_blocks
            
        except Exception as e:
            return False, f"خطأ في استخراج النص: {str(e)}"
    
    def preprocess_for_ocr(self, image):
        """تحسين الصورة لتحسين دقة OCR"""
        try:
            # تحويل إلى RGB
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # تحويل إلى numpy array للمعالجة مع OpenCV
            img_array = np.array(image)
            
            # تحويل إلى رمادي
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            
            # تطبيق تنعيم للتخلص من الضوضاء
            denoised = cv2.medianBlur(gray, 3)
            
            # تحسين التباين
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # تطبيق threshold للحصول على صورة ثنائية
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # تكبير الصورة إذا كانت صغيرة
            height, width = binary.shape
            if width < 1000:
                scale_factor = 1000 / width
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                binary = cv2.resize(binary, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            
            # تحويل العودة إلى PIL Image
            return Image.fromarray(binary)
            
        except Exception as e:
            # في حالة الخطأ، إرجاع الصورة الأصلية
            return image
    
    def translate_image_text(self, image_data, translation_function):
        """ترجمة النص في الصورة وإعادة وضعه"""
        try:
            # استخراج النص مع الإحداثيات
            success, text_blocks = self.extract_text_with_coordinates(image_data)
            if not success:
                return False, text_blocks
            
            if not text_blocks:
                return False, "لم يتم العثور على نص في الصورة"
            
            # تحميل الصورة الأصلية
            original_image = Image.open(io.BytesIO(image_data))
            
            # إنشاء نسخة للتعديل
            translated_image = original_image.copy()
            draw = ImageDraw.Draw(translated_image)
            
            # ترجمة كل كتلة نص
            for block in text_blocks:
                original_text = block['text']
                
                # ترجمة النص
                success, translated_text = translation_function(original_text)
                if not success:
                    translated_text = original_text  # استخدم النص الأصلي في حالة فشل الترجمة
                
                # مسح النص الأصلي (رسم مستطيل أبيض)
                self.clear_text_area(draw, block)
                
                # كتابة النص المترجم
                self.draw_translated_text(draw, translated_text, block)
            
            # حفظ الصورة المترجمة
            output_buffer = io.BytesIO()
            translated_image.save(output_buffer, format='PNG')
            output_buffer.seek(0)
            
            return True, output_buffer.getvalue()
            
        except Exception as e:
            return False, f"خطأ في ترجمة الصورة: {str(e)}"
    
    def clear_text_area(self, draw, text_block):
        """مسح منطقة النص الأصلي"""
        x = text_block['x']
        y = text_block['y']
        width = text_block['width']
        height = text_block['height']
        
        # رسم مستطيل أبيض لمسح النص
        draw.rectangle([x, y, x + width, y + height], fill='white', outline='white')
    
    def draw_translated_text(self, draw, text, text_block):
        """كتابة النص المترجم في المكان المناسب"""
        x = text_block['x']
        y = text_block['y']
        width = text_block['width']
        height = text_block['height']
        
        # اختيار حجم الخط المناسب
        font_size = self.calculate_font_size(text, width, height)
        font = self.fonts.get(font_size, self.fonts.get(16))
        
        if font:
            # تقسيم النص إلى أسطر إذا كان طويلاً
            lines = self.wrap_text_for_area(text, width, font)
            
            # كتابة كل سطر
            line_height = font_size + 2
            current_y = y
            
            for line in lines:
                if current_y + line_height <= y + height:  # التأكد من عدم تجاوز المنطقة
                    draw.text((x, current_y), line, fill='black', font=font)
                    current_y += line_height
                else:
                    break  # توقف إذا لم تعد هناك مساحة
        else:
            # إذا لم يتم العثور على خط، استخدم النص البسيط
            draw.text((x, y), text[:20], fill='black')  # اقتطع النص إذا كان طويلاً
    
    def calculate_font_size(self, text, width, height):
        """حساب حجم الخط المناسب للمنطقة"""
        # تقدير حجم الخط بناءً على المساحة المتاحة
        char_count = len(text)
        
        if char_count == 0:
            return 16
        
        # تقدير عرض الحرف الواحد
        estimated_char_width = width / max(1, char_count * 0.8)
        estimated_font_size = int(min(estimated_char_width * 1.5, height * 0.8))
        
        # تحديد حجم الخط ضمن النطاق المتاح
        available_sizes = sorted(self.fonts.keys())
        
        for size in available_sizes:
            if size <= estimated_font_size:
                best_size = size
            else:
                break
        else:
            best_size = available_sizes[-1] if available_sizes else 16
        
        return max(12, min(32, best_size))  # حد أدنى 12 وحد أقصى 32
    
    def wrap_text_for_area(self, text, width, font):
        """تقسيم النص إلى أسطر تناسب العرض المتاح"""
        if not font:
            return [text]
        
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            
            try:
                # قياس عرض النص
                bbox = font.getbbox(test_line)
                text_width = bbox[2] - bbox[0]
                
                if text_width <= width * 0.9:  # اترك هامش 10%
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word
            except:
                # في حالة الخطأ، استخدم تقدير بسيط
                if len(test_line) * 8 <= width:  # تقدير 8 بكسل لكل حرف
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def extract_text_simple(self, image_data, languages='ara+eng'):
        """استخراج النص البسيط من الصورة (بدون إحداثيات)"""
        try:
            image = Image.open(io.BytesIO(image_data))
            processed_image = self.preprocess_for_ocr(image)
            text = pytesseract.image_to_string(processed_image, lang=languages)
            return True, text.strip()
        except Exception as e:
            return False, f"خطأ في استخراج النص: {str(e)}"
    
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            self.temp_dir = tempfile.mkdtemp()
        except:
            pass

# إنشاء مثيل عام للاستخدام
image_processor = ImageProcessor()
