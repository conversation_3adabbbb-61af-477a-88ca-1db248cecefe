#!/usr/bin/env python3
"""
اختبار دمج نظام الترجمة مع الزر الثابت
"""

import os
import sys
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_translation_menu_creation():
    """اختبار إنشاء قائمة الترجمة تلقائياً"""
    print("🔍 اختبار إنشاء قائمة الترجمة...")
    
    try:
        from keyboards.builder import create_button, get_buttons
        
        # إنشاء زر ترجمة جديد
        translation_button_id = create_button("🌍 الترجمة", "ترجمة", None, None, 1)
        
        if translation_button_id:
            print(f"✅ تم إنشاء زر الترجمة الرئيسي: {translation_button_id}")
            
            # التحقق من إنشاء الأزرار الفرعية تلقائياً
            sub_buttons = get_buttons(translation_button_id)
            
            if len(sub_buttons) >= 2:
                print(f"✅ تم إنشاء {len(sub_buttons)} أزرار فرعية تلقائياً")
                
                # التحقق من أنواع الأزرار الفرعية
                arabic_found = False
                english_found = False
                
                for button in sub_buttons:
                    button_type = button[2]  # النوع في الفهرس 2
                    button_label = button[1]  # التسمية في الفهرس 1
                    
                    if button_type == "إلى العربية":
                        arabic_found = True
                        print(f"✅ زر الترجمة إلى العربية: {button_label}")
                    elif button_type == "إلى الإنجليزية":
                        english_found = True
                        print(f"✅ زر الترجمة إلى الإنجليزية: {button_label}")
                
                if arabic_found and english_found:
                    print("✅ تم إنشاء جميع أزرار الترجمة المطلوبة")
                    return True
                else:
                    print("❌ لم يتم إنشاء جميع أزرار الترجمة")
                    return False
            else:
                print("❌ لم يتم إنشاء الأزرار الفرعية")
                return False
        else:
            print("❌ فشل في إنشاء زر الترجمة الرئيسي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إنشاء قائمة الترجمة: {str(e)}")
        return False

def test_translation_settings():
    """اختبار إعدادات الترجمة للأزرار الفرعية"""
    print("\n🔍 اختبار إعدادات الترجمة...")
    
    try:
        from keyboards.builder import get_buttons, get_translation_settings
        
        # البحث عن زر الترجمة الرئيسي
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM buttons WHERE type = "ترجمة" ORDER BY id DESC LIMIT 1')
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            print("❌ لم يتم العثور على زر ترجمة")
            return False
        
        translation_button_id = result[0]
        sub_buttons = get_buttons(translation_button_id)
        
        settings_found = 0
        
        for button in sub_buttons:
            button_id = button[0]
            button_type = button[2]
            
            # التحقق من وجود إعدادات الترجمة
            settings = get_translation_settings(button_id)
            
            if settings:
                settings_found += 1
                translation_type = settings[2]  # نوع الترجمة
                print(f"✅ إعدادات الترجمة موجودة للزر {button_type}: {translation_type}")
            else:
                print(f"❌ إعدادات الترجمة مفقودة للزر {button_type}")
        
        if settings_found >= 2:
            print("✅ جميع إعدادات الترجمة موجودة")
            return True
        else:
            print("❌ بعض إعدادات الترجمة مفقودة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات الترجمة: {str(e)}")
        return False

def test_button_action_processing():
    """اختبار معالجة إجراءات الأزرار"""
    print("\n🔍 اختبار معالجة إجراءات الأزرار...")
    
    try:
        from keyboards.builder import process_button_action, get_buttons
        
        # البحث عن زر الترجمة الرئيسي
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM buttons WHERE type = "ترجمة" ORDER BY id DESC LIMIT 1')
        main_button = cursor.fetchone()
        conn.close()
        
        if not main_button:
            print("❌ لم يتم العثور على زر ترجمة رئيسي")
            return False
        
        # اختبار معالجة الزر الرئيسي
        action_type, action_data = process_button_action(main_button, user_id=123)
        
        if action_type == "translation_menu":
            print("✅ زر الترجمة الرئيسي يعمل كقائمة")
        else:
            print(f"❌ زر الترجمة الرئيسي يعطي نوع خاطئ: {action_type}")
            return False
        
        # اختبار الأزرار الفرعية
        sub_buttons = get_buttons(main_button[0])
        
        for button in sub_buttons:
            action_type, action_data = process_button_action(button, user_id=123)
            button_type = button[2]
            
            if button_type == "إلى العربية" and action_type == "translation_to_arabic":
                print("✅ زر الترجمة إلى العربية يعمل بشكل صحيح")
            elif button_type == "إلى الإنجليزية" and action_type == "translation_to_english":
                print("✅ زر الترجمة إلى الإنجليزية يعمل بشكل صحيح")
            else:
                print(f"❌ زر {button_type} يعطي نوع خاطئ: {action_type}")
                return False
        
        print("✅ جميع أزرار الترجمة تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الأزرار: {str(e)}")
        return False

def test_database_structure():
    """اختبار بنية قاعدة البيانات"""
    print("\n🔍 اختبار بنية قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        tables_to_check = ['translation_settings', 'translation_global_settings']
        
        for table in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
                conn.close()
                return False
        
        # التحقق من البيانات في الجداول
        cursor.execute('SELECT COUNT(*) FROM translation_settings')
        settings_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM translation_global_settings')
        global_settings_count = cursor.fetchone()[0]
        
        print(f"✅ عدد إعدادات الترجمة: {settings_count}")
        print(f"✅ عدد الإعدادات العامة: {global_settings_count}")
        
        conn.close()
        
        if settings_count > 0 and global_settings_count > 0:
            print("✅ بنية قاعدة البيانات صحيحة")
            return True
        else:
            print("⚠️ قاعدة البيانات فارغة ولكن البنية صحيحة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    print("\n🧹 تنظيف بيانات الاختبار...")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # حذف أزرار الاختبار
        cursor.execute('DELETE FROM buttons WHERE label LIKE "%الترجمة%" AND created_by = 1')
        deleted_buttons = cursor.rowcount
        
        # حذف إعدادات الترجمة المرتبطة
        cursor.execute('DELETE FROM translation_settings WHERE button_id NOT IN (SELECT id FROM buttons)')
        deleted_settings = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم حذف {deleted_buttons} أزرار اختبار")
        print(f"✅ تم حذف {deleted_settings} إعدادات ترجمة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات الدمج"""
    print("🚀 بدء اختبار دمج نظام الترجمة")
    print("=" * 50)
    
    tests = [
        ("بنية قاعدة البيانات", test_database_structure),
        ("إنشاء قائمة الترجمة", test_translation_menu_creation),
        ("إعدادات الترجمة", test_translation_settings),
        ("معالجة إجراءات الأزرار", test_button_action_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    # تنظيف البيانات
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات الدمج نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
