"""
نظام ترجمة PDF محسن ومطور
يحل مشاكل النص العربي ويحافظ على التنسيق بدقة عالية
مصمم للعمل على Linux بكفاءة قصوى
"""

import os
import io
import tempfile
import logging
from pathlib import Path
from typing import Tuple, Optional, Dict, Any, List
import fitz  # PyMuPDF
from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from pdf2docx import Converter
import arabic_reshaper
from bidi.algorithm import get_display
from deep_translator import GoogleTranslator
import cv2
import numpy as np
from PIL import Image, ImageFont, ImageDraw
import concurrent.futures
import threading
import time

# إعداد التسجيل - فقط للملف، بدون التيرمنال
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)8s] %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler('pdf_translation.log', encoding='utf-8')
        # تم حذف StreamHandler لإخفاء الرسائل من التيرمنال
    ]
)
logger = logging.getLogger(__name__)

class EnhancedPDFTranslator:
    """
    نظام ترجمة PDF محسن ومطور
    يحل مشاكل النص العربي ويحافظ على التنسيق
    """
    
    def __init__(self):
        """تهيئة النظام المحسن"""
        self.temp_dir = tempfile.mkdtemp()
        self.arabic_fonts = self.setup_arabic_fonts()
        self.translation_cache = {}  # تخزين مؤقت للترجمات
        self.setup_arabic_support()
        # تم تهيئة نظام ترجمة PDF المحسن
    
    def setup_arabic_fonts(self) -> List[str]:
        """إعداد الخطوط العربية المدعومة"""
        arabic_fonts = []
        
        # قائمة الخطوط العربية المتوفرة على Linux
        font_candidates = [
            # خطوط Noto (الأفضل للعربية)
            "/usr/share/fonts/truetype/noto/NotoNaskhArabic-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoKufiArabic-Regular.ttf",
            
            # خطوط DejaVu (متوفرة عادة)
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            
            # خطوط Liberation (بديل جيد)
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",
            
            # خطوط Ubuntu (إذا كان متوفراً)
            "/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf",
            "/usr/share/fonts/truetype/ubuntu/Ubuntu-B.ttf",
            
            # مسارات إضافية محتملة
            "/System/Library/Fonts/Arial.ttf",  # macOS
            "/usr/share/fonts/TTF/DejaVuSans.ttf",  # Arch Linux
            "/usr/share/fonts/truetype/ttf-dejavu/DejaVuSans.ttf",  # بعض التوزيعات
        ]
        
        for font_path in font_candidates:
            if os.path.exists(font_path):
                arabic_fonts.append(font_path)
                # تم إزالة رسالة logger لتجنب الإزعاج في التيرمنال

        if not arabic_fonts:
            # تم إزالة رسالة logger لتجنب الإزعاج في التيرمنال
            # إنشاء خط افتراضي بسيط
            arabic_fonts.append("default")
        
        return arabic_fonts
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية المحسن"""
        try:
            # اختبار arabic_reshaper
            test_text = "مرحبا بالعالم"
            self.arabic_reshaper_available = True
            arabic_reshaper.reshape(test_text)
            
            # اختبار python-bidi
            self.bidi_available = True
            get_display(test_text)
            
            # دعم اللغة العربية متوفر بالكامل
        except Exception as e:
            # مشكلة في دعم العربية
            self.arabic_reshaper_available = False
            self.bidi_available = False
    
    def detect_language(self, text: str) -> str:
        """كشف لغة النص المحسن"""
        if not text or not text.strip():
            return 'unknown'
        
        # عد الأحرف العربية
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        # عد الأحرف اللاتينية
        latin_chars = sum(1 for char in text if 'a' <= char.lower() <= 'z')
        total_chars = arabic_chars + latin_chars
        
        if total_chars == 0:
            return 'unknown'
        
        arabic_ratio = arabic_chars / total_chars
        
        if arabic_ratio > 0.3:
            return 'arabic'
        elif latin_chars > arabic_chars:
            return 'english'
        else:
            return 'mixed'
    
    def fix_arabic_text_enhanced(self, text: str) -> str:
        """إصلاح النص العربي المحسن"""
        try:
            if not text or not text.strip():
                return text
            
            # تنظيف النص المحسن
            cleaned_text = self.clean_text_enhanced(text)
            
            # إعادة تشكيل الأحرف العربية
            if self.arabic_reshaper_available:
                try:
                    # محاولة استخدام الإعدادات المحسنة
                    configuration = {
                        'delete_harakat': False,  # الحفاظ على الحركات
                        'support_zwj': True,      # دعم Zero Width Joiner
                        'use_unshaped_instead_of_isolated': False
                    }
                    reshaped_text = arabic_reshaper.reshape(cleaned_text, configuration)
                except TypeError:
                    # إذا فشلت الطريقة المحسنة، استخدم الطريقة البسيطة
                    reshaped_text = arabic_reshaper.reshape(cleaned_text)
            else:
                reshaped_text = cleaned_text
            
            # إصلاح اتجاه النص
            if self.bidi_available:
                final_text = get_display(reshaped_text)
            else:
                final_text = reshaped_text
            
            return final_text
        except Exception as e:
            # خطأ في إصلاح النص العربي
            return text
    
    def clean_text_enhanced(self, text: str) -> str:
        """تنظيف النص المحسن"""
        import re
        
        # إزالة الأحرف التحكمية المشكلة
        text = re.sub(r'[\u200e\u200f\u202a\u202b\u202c\u202d\u202e]', '', text)
        # إزالة Zero Width characters
        text = re.sub(r'[\u200b\u200c\u200d\ufeff]', '', text)
        # تنظيف المسافات المتعددة
        text = re.sub(r'\s+', ' ', text)
        # إزالة المسافات في البداية والنهاية
        text = text.strip()
        
        return text
    
    def translate_text_cached(self, text: str, target_lang: str = 'ar') -> str:
        """ترجمة النص مع التخزين المؤقت للسرعة"""
        if not text or not text.strip():
            return text
        
        # إنشاء مفتاح للتخزين المؤقت
        cache_key = f"{text}_{target_lang}"
        
        # التحقق من التخزين المؤقت
        if cache_key in self.translation_cache:
            return self.translation_cache[cache_key]
        
        try:
            # تحديد اللغة المصدر
            source_lang = 'auto'
            
            # إنشاء المترجم
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            
            # ترجمة النص
            translated = translator.translate(text)
            
            # إصلاح النص العربي إذا كانت الترجمة للعربية
            if target_lang == 'ar':
                translated = self.fix_arabic_text_enhanced(translated)
            
            # حفظ في التخزين المؤقت
            self.translation_cache[cache_key] = translated
            
            return translated
        except Exception as e:
            # خطأ في الترجمة
            return text
    
    def get_best_font_for_text(self, text: str, font_size: float) -> str:
        """اختيار أفضل خط للنص"""
        lang = self.detect_language(text)
        
        if lang == 'arabic' or lang == 'mixed':
            # للنصوص العربية، استخدم أول خط عربي متوفر
            if self.arabic_fonts and self.arabic_fonts[0] != "default":
                return self.arabic_fonts[0]
        
        # للنصوص الإنجليزية أو الافتراضية
        return "helv"  # خط Helvetica الافتراضي في PyMuPDF
    
    def calculate_text_dimensions(self, text: str, font_size: float, font_path: str = None) -> Tuple[float, float]:
        """حساب أبعاد النص بدقة"""
        try:
            if font_path and font_path != "default" and os.path.exists(font_path):
                # استخدام PIL لحساب أبعاد النص بدقة
                font = ImageFont.truetype(font_path, int(font_size))
                
                # إنشاء صورة مؤقتة لحساب الأبعاد
                img = Image.new('RGB', (1000, 100), color='white')
                draw = ImageDraw.Draw(img)
                
                # حساب أبعاد النص
                bbox = draw.textbbox((0, 0), text, font=font)
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]
                
                return width, height
            else:
                # تقدير تقريبي للأبعاد
                char_width = font_size * 0.6  # تقدير عرض الحرف
                width = len(text) * char_width
                height = font_size * 1.2
                
                return width, height
                
        except Exception as e:
            # خطأ في حساب أبعاد النص
            # تقدير افتراضي
            return len(text) * font_size * 0.6, font_size * 1.2
    
    def insert_text_enhanced(self, page, text: str, bbox: tuple, font_size: float, 
                           target_lang: str, original_font: str = None) -> bool:
        """إدراج النص المحسن مع دعم RTL والخطوط العربية"""
        try:
            # اختيار أفضل خط
            font_path = self.get_best_font_for_text(text, font_size)
            
            # حساب أبعاد النص
            text_width, text_height = self.calculate_text_dimensions(text, font_size, font_path)
            
            # حساب موقع النص
            x, y, x2, y2 = bbox
            bbox_width = x2 - x
            bbox_height = y2 - y
            
            # تحديد موقع البداية حسب اللغة
            if target_lang == 'ar':
                # للعربية - البداية من اليمين
                start_x = x2 - 5  # مسافة صغيرة من الحافة
                text_align = 2  # محاذاة يمين
            else:
                # للإنجليزية - البداية من اليسار
                start_x = x + 5
                text_align = 0  # محاذاة يسار
            
            start_y = y + font_size * 0.8
            
            # التأكد من أن النص يدخل في المساحة المتاحة
            if text_width > bbox_width:
                # تقسيم النص إلى أسطر متعددة
                return self.insert_multiline_text(page, text, bbox, font_size, target_lang, font_path)
            
            try:
                # محاولة استخدام textbox للنصوص الطويلة أو العربية
                if target_lang == 'ar' or len(text) > 50:
                    rect = fitz.Rect(x, y, x2, y2)
                    
                    # إعداد الخط
                    if font_path != "default" and font_path != "helv":
                        # محاولة تحميل خط مخصص
                        try:
                            font_buffer = open(font_path, "rb").read()
                            font_name = "arabic_font"
                            page.parent.insert_font(fontname=font_name, fontbuffer=font_buffer)
                            font_to_use = font_name
                        except:
                            font_to_use = "helv"
                    else:
                        font_to_use = "helv"
                    
                    # إدراج النص باستخدام textbox
                    result = page.insert_textbox(
                        rect,
                        text,
                        fontsize=font_size,
                        fontname=font_to_use,
                        color=(0, 0, 0),
                        align=text_align,
                        fill=(1, 1, 1)  # خلفية بيضاء
                    )
                    
                    if result > 0:  # نجح الإدراج
                        return True
                
                # إذا فشل textbox، استخدم insert_text العادي
                page.insert_text(
                    (start_x, start_y),
                    text,
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
                
                return True
                
            except Exception as insert_error:
                # خطأ في إدراج النص
                
                # محاولة أخيرة بالطريقة البسيطة
                try:
                    page.insert_text(
                        (start_x, start_y),
                        text,
                        fontsize=font_size,
                        color=(0, 0, 0)
                    )
                    return True
                except:
                    return False
                    
        except Exception as e:
            # خطأ عام في إدراج النص المحسن
            return False
    
    def insert_multiline_text(self, page, text: str, bbox: tuple, font_size: float, 
                            target_lang: str, font_path: str) -> bool:
        """إدراج نص متعدد الأسطر"""
        try:
            x, y, x2, y2 = bbox
            bbox_width = x2 - x
            bbox_height = y2 - y
            
            # تقسيم النص إلى كلمات
            words = text.split()
            lines = []
            current_line = ""
            
            for word in words:
                test_line = current_line + " " + word if current_line else word
                test_width, _ = self.calculate_text_dimensions(test_line, font_size, font_path)
                
                if test_width <= bbox_width - 10:  # مسافة أمان
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                        current_line = word
                    else:
                        lines.append(word)  # كلمة طويلة جداً
            
            if current_line:
                lines.append(current_line)
            
            # إدراج كل سطر
            line_height = font_size * 1.2
            for i, line in enumerate(lines):
                line_y = y + (i + 1) * line_height
                
                if line_y > y2:  # تجاوز حدود المربع
                    break
                
                if target_lang == 'ar':
                    line_x = x2 - 5
                else:
                    line_x = x + 5
                
                page.insert_text(
                    (line_x, line_y),
                    line,
                    fontsize=font_size,
                    color=(0, 0, 0)
                )
            
            return True
            
        except Exception as e:
            # خطأ في إدراج النص متعدد الأسطر
            return False

    def process_pdf_enhanced(self, pdf_data: bytes, target_lang: str = 'ar',
                           progress_callback=None) -> Tuple[bool, bytes, str]:
        """معالجة PDF محسنة مع حل مشاكل النص العربي"""
        try:
            # فتح PDF
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            total_pages = len(doc)

            if progress_callback:
                progress_callback(0, f"📄 بدء معالجة PDF محسنة ({total_pages} صفحة)")

            # إحصائيات المعالجة
            successful_replacements = 0
            failed_replacements = 0

            # معالجة كل صفحة
            for page_num in range(total_pages):
                page = doc[page_num]

                # تحديث التقدم
                progress_percent = int((page_num / total_pages) * 90)  # 90% للمعالجة
                if progress_callback:
                    progress_callback(progress_percent,
                                    f"🔄 معالجة الصفحة {page_num + 1}/{total_pages} ({progress_percent}%)")

                # استخراج النصوص مع المواقع والتنسيق
                text_dict = page.get_text("dict")

                # جمع جميع النصوص للترجمة المجمعة (للسرعة)
                texts_to_translate = []
                text_info = []

                for block in text_dict["blocks"]:
                    if "lines" in block:  # كتلة نص
                        for line in block["lines"]:
                            for span in line["spans"]:
                                original_text = span["text"].strip()

                                if original_text and len(original_text) > 1:
                                    texts_to_translate.append(original_text)
                                    text_info.append({
                                        'text': original_text,
                                        'bbox': span["bbox"],
                                        'font_size': span["size"],
                                        'font_name': span["font"],
                                        'flags': span.get("flags", 0)
                                    })

                # ترجمة جميع النصوص دفعة واحدة (للسرعة)
                translated_texts = self.batch_translate(texts_to_translate, target_lang)

                # استبدال النصوص
                for i, info in enumerate(text_info):
                    if i < len(translated_texts):
                        original_text = info['text']
                        translated_text = translated_texts[i]
                        bbox = info['bbox']
                        font_size = info['font_size']

                        # حذف النص الأصلي
                        rect = fitz.Rect(bbox)
                        page.add_redact_annot(rect)

                        # إدراج النص المترجم المحسن
                        success = self.insert_text_enhanced(
                            page, translated_text, bbox, font_size, target_lang
                        )

                        if success:
                            successful_replacements += 1
                        else:
                            failed_replacements += 1

                # تطبيق التغييرات
                page.apply_redactions()

            # تحديث التقدم - الانتهاء
            if progress_callback:
                progress_callback(100, "✅ تم الانتهاء من الترجمة المحسنة - جاري حفظ الملف...")

            # حفظ PDF المعدل
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            doc.close()

            output_buffer.seek(0)
            result_data = output_buffer.getvalue()

            # رسالة النتيجة
            success_rate = (successful_replacements / (successful_replacements + failed_replacements)) * 100 if (successful_replacements + failed_replacements) > 0 else 100
            message = f"تم ترجمة PDF بنجاح (معدل النجاح: {success_rate:.1f}%)"

            return True, result_data, message

        except Exception as e:
            # خطأ في معالجة PDF المحسنة

            # في حالة الفشل، استخدم الطريقة البديلة
            return self.fallback_pdf_translation(pdf_data, target_lang, progress_callback)

    def batch_translate(self, texts: List[str], target_lang: str) -> List[str]:
        """ترجمة مجمعة للنصوص لتحسين السرعة"""
        translated_texts = []

        # تجميع النصوص القصيرة معاً
        batch_size = 10
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]

            # ترجمة كل نص في المجموعة
            batch_results = []
            for text in batch:
                translated = self.translate_text_cached(text, target_lang)
                batch_results.append(translated)

            translated_texts.extend(batch_results)

        return translated_texts

    def fallback_pdf_translation(self, pdf_data: bytes, target_lang: str,
                                progress_callback=None) -> Tuple[bool, bytes, str]:
        """طريقة بديلة: PDF → Word → ترجمة → PDF"""
        try:
            if progress_callback:
                progress_callback(10, "🔄 استخدام الطريقة البديلة: تحويل PDF إلى Word...")

            # تحويل PDF إلى Word
            pdf_path = os.path.join(self.temp_dir, "temp_input.pdf")
            docx_path = os.path.join(self.temp_dir, "temp_converted.docx")

            with open(pdf_path, 'wb') as f:
                f.write(pdf_data)

            # التحويل باستخدام pdf2docx
            cv = Converter(pdf_path)
            cv.convert(docx_path, start=0, end=None)
            cv.close()

            if progress_callback:
                progress_callback(30, "📝 ترجمة المستند...")

            # قراءة وترجمة Word
            with open(docx_path, 'rb') as f:
                docx_data = f.read()

            # ترجمة Word باستخدام النظام المحسن
            success, translated_docx, message = self.process_word_enhanced(
                docx_data, target_lang, progress_callback
            )

            if not success:
                return False, b"", "فشل في ترجمة المستند"

            if progress_callback:
                progress_callback(80, "📄 تحويل Word المترجم إلى PDF...")

            # حفظ Word المترجم
            translated_docx_path = os.path.join(self.temp_dir, "temp_translated.docx")
            with open(translated_docx_path, 'wb') as f:
                f.write(translated_docx)

            # تحويل Word المترجم إلى PDF باستخدام LibreOffice
            output_pdf_path = os.path.join(self.temp_dir, "temp_output.pdf")

            import subprocess
            try:
                subprocess.run([
                    'libreoffice', '--headless', '--convert-to', 'pdf',
                    '--outdir', self.temp_dir, translated_docx_path
                ], check=True, capture_output=True, timeout=60)

                # البحث عن ملف PDF الناتج
                for file in os.listdir(self.temp_dir):
                    if file.endswith('.pdf') and 'temp_translated' in file:
                        output_pdf_path = os.path.join(self.temp_dir, file)
                        break

                # قراءة PDF الناتج
                if os.path.exists(output_pdf_path):
                    with open(output_pdf_path, 'rb') as f:
                        result_data = f.read()

                    # تنظيف الملفات المؤقتة
                    for temp_file in [pdf_path, docx_path, translated_docx_path, output_pdf_path]:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)

                    return True, result_data, "تم الترجمة باستخدام الطريقة البديلة"
                else:
                    return False, b"", "فشل في تحويل Word إلى PDF"

            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                # إذا لم يكن LibreOffice متوفراً، أرجع Word المترجم
                return True, translated_docx, "تم الترجمة (تم إرجاع ملف Word)"

        except Exception as e:
            # خطأ في الطريقة البديلة
            return False, b"", f"خطأ في الطريقة البديلة: {str(e)}"

    def process_word_enhanced(self, docx_data: bytes, target_lang: str = 'ar',
                            progress_callback=None) -> Tuple[bool, bytes, str]:
        """معالجة Word محسنة مع ترجمة متوازية"""
        try:
            # فتح المستند
            doc = Document(io.BytesIO(docx_data))

            # حساب إجمالي العناصر للمعالجة
            all_paragraphs = [p for p in doc.paragraphs if p.text.strip()]
            all_table_cells = []

            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                all_table_cells.append(paragraph)

            total_elements = len(all_paragraphs) + len(all_table_cells)
            processed_elements = 0

            if progress_callback:
                progress_callback(0, f"📝 بدء معالجة Word محسنة ({total_elements} عنصر)")

            # معالجة الفقرات الرئيسية
            for paragraph in all_paragraphs:
                processed_elements += 1
                progress_percent = int((processed_elements / total_elements) * 80)
                if progress_callback:
                    progress_callback(progress_percent,
                                    f"🔄 ترجمة الفقرة {processed_elements}/{total_elements} ({progress_percent}%)")

                self.translate_paragraph_enhanced(paragraph, target_lang)

            # معالجة الجداول
            if progress_callback:
                progress_callback(80, "📊 معالجة الجداول...")

            for paragraph in all_table_cells:
                processed_elements += 1
                progress_percent = 80 + int(((processed_elements - len(all_paragraphs)) / len(all_table_cells)) * 15) if all_table_cells else 95
                if progress_callback:
                    progress_callback(progress_percent,
                                    f"🔄 ترجمة جدول {processed_elements}/{total_elements} ({progress_percent}%)")

                self.translate_paragraph_enhanced(paragraph, target_lang)

            # تحديث التقدم - الانتهاء
            if progress_callback:
                progress_callback(100, "✅ تم الانتهاء من الترجمة المحسنة - جاري حفظ الملف...")

            # حفظ المستند
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            output_buffer.seek(0)

            return True, output_buffer.getvalue(), "تم ترجمة Word بنجاح مع التحسينات"

        except Exception as e:
            # خطأ في معالجة Word المحسنة
            return False, b"", f"خطأ في الترجمة: {str(e)}"

    def translate_paragraph_enhanced(self, paragraph, target_lang: str):
        """ترجمة فقرة محسنة مع الحفاظ على التنسيق"""
        try:
            original_text = paragraph.text

            # ترجمة النص
            translated_text = self.translate_text_cached(original_text, target_lang)

            # حفظ التنسيق الأصلي
            original_runs = list(paragraph.runs)
            original_alignment = paragraph.alignment

            # مسح النص الحالي
            paragraph.clear()

            # إضافة النص المترجم
            new_run = paragraph.add_run(translated_text)

            # نسخ التنسيق من أول run
            if original_runs:
                first_run = original_runs[0]
                if first_run.font.name:
                    new_run.font.name = first_run.font.name
                if first_run.font.size:
                    new_run.font.size = first_run.font.size
                new_run.bold = first_run.bold
                new_run.italic = first_run.italic
                new_run.underline = first_run.underline

            # تحديد اتجاه النص
            if target_lang == 'ar':
                # للعربية - RTL
                paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                try:
                    paragraph._element.set(qn('w:bidi'), '1')
                except:
                    pass
            else:
                # للإنجليزية - LTR
                paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT

        except Exception as e:
            # خطأ في ترجمة الفقرة المحسنة
            pass

    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            # تم تنظيف الملفات المؤقتة
        except Exception as e:
            # خطأ في التنظيف
            pass

    def __del__(self):
        """تنظيف تلقائي عند حذف الكائن"""
        self.cleanup()

# إنشاء مثيل عام للاستخدام
enhanced_pdf_translator = EnhancedPDFTranslator()

def translate_pdf_enhanced(file_data: bytes, file_name: str,
                         target_lang: str = 'ar', progress_callback=None) -> Tuple[bool, bytes, str]:
    """دالة مساعدة للترجمة المحسنة"""
    file_ext = Path(file_name).suffix.lower()

    if file_ext == '.pdf':
        return enhanced_pdf_translator.process_pdf_enhanced(file_data, target_lang, progress_callback)
    elif file_ext == '.docx':
        return enhanced_pdf_translator.process_word_enhanced(file_data, target_lang, progress_callback)
    else:
        return False, b"", f"نوع الملف غير مدعوم: {file_ext}"
