#!/usr/bin/env python3
"""
أداة سطر الأوامر لاختبار نظام ترجمة PDF النهائي
"""

import os
import sys
import argparse
import time
from pathlib import Path
import json

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ultimate_pdf_translator import ultimate_translator, translate_pdf_ultimate, advanced_logger

class PDFTranslatorCLI:
    """واجهة سطر الأوامر لنظام ترجمة PDF"""
    
    def __init__(self):
        self.results = []
    
    def translate_single_file(self, input_path, output_path=None, target_lang='ar', verbose=False):
        """ترجمة ملف واحد"""
        try:
            input_file = Path(input_path)
            if not input_file.exists():
                print(f"❌ الملف غير موجود: {input_path}")
                return False
            
            if not output_path:
                output_path = input_file.parent / f"translated_{input_file.name}"
            
            print(f"🔄 بدء ترجمة: {input_file.name}")
            print(f"📁 المخرجات: {output_path}")
            
            # قراءة الملف
            with open(input_file, 'rb') as f:
                file_data = f.read()
            
            # دالة التقدم
            def progress_callback(percent, status):
                if verbose:
                    print(f"\r[{'█' * (percent // 5)}{'░' * (20 - (percent // 5))}] {percent}% - {status}", end="", flush=True)
            
            # بدء الترجمة
            start_time = time.time()
            success, translated_data, message = translate_pdf_ultimate(
                file_data, input_file.name, target_lang, progress_callback
            )
            end_time = time.time()
            
            if verbose:
                print()  # سطر جديد بعد شريط التقدم
            
            if success:
                # حفظ النتيجة
                with open(output_path, 'wb') as f:
                    f.write(translated_data)
                
                # إحصائيات
                original_size = len(file_data)
                translated_size = len(translated_data)
                processing_time = end_time - start_time
                
                result = {
                    'file': str(input_file),
                    'success': True,
                    'message': message,
                    'original_size': original_size,
                    'translated_size': translated_size,
                    'processing_time': processing_time,
                    'output_file': str(output_path)
                }
                
                print(f"✅ نجحت الترجمة: {message}")
                print(f"📊 الحجم الأصلي: {original_size:,} بايت")
                print(f"📊 الحجم المترجم: {translated_size:,} بايت")
                print(f"⏱️ وقت المعالجة: {processing_time:.2f} ثانية")
                print(f"💾 تم الحفظ في: {output_path}")
                
            else:
                result = {
                    'file': str(input_file),
                    'success': False,
                    'message': message,
                    'processing_time': end_time - start_time
                }
                
                print(f"❌ فشلت الترجمة: {message}")
            
            self.results.append(result)
            return success
            
        except Exception as e:
            print(f"❌ خطأ في معالجة الملف: {e}")
            self.results.append({
                'file': str(input_path),
                'success': False,
                'message': str(e)
            })
            return False
    
    def translate_batch(self, input_dir, output_dir=None, target_lang='ar', verbose=False):
        """ترجمة مجموعة ملفات"""
        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ المجلد غير موجود: {input_dir}")
            return
        
        if not output_dir:
            output_dir = input_path.parent / f"translated_{input_path.name}"
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # البحث عن ملفات PDF
        pdf_files = list(input_path.glob("*.pdf"))
        
        if not pdf_files:
            print(f"❌ لم يتم العثور على ملفات PDF في: {input_dir}")
            return
        
        print(f"📁 تم العثور على {len(pdf_files)} ملف PDF")
        print(f"📂 مجلد المخرجات: {output_path}")
        print("=" * 60)
        
        successful = 0
        failed = 0
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n📄 [{i}/{len(pdf_files)}] معالجة: {pdf_file.name}")
            
            output_file = output_path / f"translated_{pdf_file.name}"
            
            if self.translate_single_file(pdf_file, output_file, target_lang, verbose):
                successful += 1
            else:
                failed += 1
        
        # ملخص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص النتائج:")
        print(f"✅ نجح: {successful} ملف")
        print(f"❌ فشل: {failed} ملف")
        print(f"📈 معدل النجاح: {(successful / len(pdf_files)) * 100:.1f}%")
        
        # حفظ تقرير مفصل
        self.save_report(output_path / "translation_report.json")
    
    def save_report(self, report_path):
        """حفظ تقرير مفصل"""
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            print(f"📋 تم حفظ التقرير في: {report_path}")
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
    
    def test_system(self):
        """اختبار النظام"""
        print("🧪 اختبار النظام...")
        
        # اختبار المتطلبات
        print("🔍 فحص المتطلبات...")
        
        required_modules = ['fitz', 'PIL', 'arabic_reshaper', 'bidi', 'deep_translator']
        missing = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"   ✅ {module}")
            except ImportError:
                print(f"   ❌ {module}")
                missing.append(module)
        
        if missing:
            print(f"⚠️ مكتبات مفقودة: {', '.join(missing)}")
            return False
        
        # اختبار الخطوط
        print("\n🔤 فحص الخطوط العربية...")
        fonts = ultimate_translator.font_manager.available_fonts
        print(f"   📊 تم العثور على {len(fonts)} خط")
        
        best_font = ultimate_translator.font_manager.get_best_arabic_font()
        if best_font:
            print(f"   ✅ أفضل خط: {best_font}")
        else:
            print("   ⚠️ لم يتم العثور على خط عربي مناسب")
        
        # اختبار الترجمة
        print("\n🔄 اختبار الترجمة...")
        test_text = "Hello World"
        try:
            translated = ultimate_translator._translate_text_cached(test_text, 'ar')
            print(f"   ✅ ترجمة تجريبية: '{test_text}' → '{translated}'")
        except Exception as e:
            print(f"   ❌ فشل في الترجمة: {e}")
            return False
        
        print("\n✅ النظام جاهز للاستخدام!")
        return True

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(
        description="نظام ترجمة PDF النهائي",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:

  # ترجمة ملف واحد
  python pdf_translator_cli.py -f document.pdf -o translated_document.pdf

  # ترجمة مجموعة ملفات
  python pdf_translator_cli.py -d input_folder -o output_folder

  # ترجمة إلى الإنجليزية مع تفاصيل
  python pdf_translator_cli.py -f document.pdf -l en -v

  # اختبار النظام
  python pdf_translator_cli.py --test
        """
    )
    
    parser.add_argument('-f', '--file', help='ملف PDF للترجمة')
    parser.add_argument('-d', '--directory', help='مجلد يحتوي على ملفات PDF')
    parser.add_argument('-o', '--output', help='مسار المخرجات')
    parser.add_argument('-l', '--language', default='ar', choices=['ar', 'en'], 
                       help='اللغة المستهدفة (افتراضي: ar)')
    parser.add_argument('-v', '--verbose', action='store_true', 
                       help='عرض تفاصيل أكثر')
    parser.add_argument('--test', action='store_true', 
                       help='اختبار النظام')
    
    args = parser.parse_args()
    
    cli = PDFTranslatorCLI()
    
    if args.test:
        cli.test_system()
    elif args.file:
        cli.translate_single_file(args.file, args.output, args.language, args.verbose)
    elif args.directory:
        cli.translate_batch(args.directory, args.output, args.language, args.verbose)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
