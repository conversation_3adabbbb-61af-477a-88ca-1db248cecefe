#!/usr/bin/env python3
"""
اختبار النظام الاحترافي لترجمة المستندات
مصمم للعمل على Linux Server بدون مشاكل
"""

import os
import sys
import io
import tempfile
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_requirements():
    """اختبار متطلبات النظام"""
    print("🔍 اختبار متطلبات النظام...")
    
    required_modules = [
        'fitz',  # PyMuPDF
        'docx',  # python-docx
        'pdf2docx',  # pdf2docx
        'arabic_reshaper',  # arabic_reshaper
        'bidi',  # python-bidi
        'deep_translator',  # deep_translator
        'cv2',  # opencv-python-headless
        'PIL',  # Pillow
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - غير متوفر")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_modules)}")
        return False
    else:
        print("✅ جميع المتطلبات متوفرة")
        return True

def test_professional_translator_import():
    """اختبار استيراد النظام الاحترافي"""
    print("\n🔍 اختبار استيراد النظام الاحترافي...")
    
    try:
        from professional_document_translator import (
            ProfessionalDocumentTranslator,
            professional_translator,
            translate_document_file,
            get_file_info
        )
        print("✅ تم استيراد النظام الاحترافي بنجاح")
        return True, professional_translator
    except Exception as e:
        print(f"❌ فشل في استيراد النظام: {e}")
        return False, None

def create_test_pdf_with_arabic():
    """إنشاء PDF تجريبي يحتوي على نص عربي وإنجليزي"""
    print("\n🔍 إنشاء PDF تجريبي...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # إنشاء PDF
        output_buffer = io.BytesIO()
        c = canvas.Canvas(output_buffer, pagesize=A4)
        
        # إضافة نصوص إنجليزية
        c.drawString(100, 750, "Professional Document Translation System")
        c.drawString(100, 720, "This is a test document for translation.")
        c.drawString(100, 690, "It contains English text that will be translated to Arabic.")
        c.drawString(100, 660, "The system preserves formatting and images.")
        
        # إضافة نص عربي (للاختبار العكسي)
        c.drawString(100, 600, "هذا نص عربي تجريبي")
        c.drawString(100, 570, "سيتم ترجمته إلى الإنجليزية")
        
        # إضافة عنوان
        c.setFont("Helvetica-Bold", 16)
        c.drawString(100, 500, "Test Document Title")
        
        # إضافة فقرة طويلة
        c.setFont("Helvetica", 12)
        long_text = """This is a longer paragraph that tests the system's ability to handle 
multiple lines of text. The professional translation system should maintain 
the structure and formatting while providing accurate translations."""
        
        y_position = 450
        for line in long_text.split('\n'):
            c.drawString(100, y_position, line.strip())
            y_position -= 20
        
        c.showPage()
        c.save()
        
        output_buffer.seek(0)
        pdf_data = output_buffer.getvalue()
        
        print(f"✅ تم إنشاء PDF تجريبي (حجم: {len(pdf_data)} بايت)")
        return pdf_data
        
    except Exception as e:
        print(f"❌ فشل في إنشاء PDF: {e}")
        return None

def create_test_docx_with_arabic():
    """إنشاء Word تجريبي يحتوي على نص عربي وإنجليزي"""
    print("\n🔍 إنشاء Word تجريبي...")
    
    try:
        from docx import Document
        from docx.shared import Inches
        
        # إنشاء مستند جديد
        doc = Document()
        
        # إضافة عنوان
        title = doc.add_heading('Professional Document Translation Test', 0)
        
        # إضافة فقرات إنجليزية
        doc.add_paragraph('This is a comprehensive test document for the professional translation system.')
        doc.add_paragraph('The system is designed to work efficiently on Linux servers without GUI dependencies.')
        doc.add_paragraph('It preserves formatting, images, tables, and handles Arabic RTL text correctly.')
        
        # إضافة فقرة عربية
        arabic_para = doc.add_paragraph('هذا نص عربي تجريبي لاختبار النظام الاحترافي للترجمة.')
        
        # إضافة جدول
        table = doc.add_table(rows=3, cols=2)
        table.style = 'Table Grid'
        
        # ملء الجدول
        cells = [
            ['English Text', 'Arabic Text'],
            ['Hello World', 'مرحبا بالعالم'],
            ['Professional System', 'نظام احترافي']
        ]
        
        for i, row_data in enumerate(cells):
            row = table.rows[i]
            for j, cell_text in enumerate(row_data):
                row.cells[j].text = cell_text
        
        # إضافة فقرة أخيرة
        doc.add_paragraph('This document tests various formatting elements and multilingual content.')
        
        # حفظ في buffer
        output_buffer = io.BytesIO()
        doc.save(output_buffer)
        output_buffer.seek(0)
        
        docx_data = output_buffer.getvalue()
        print(f"✅ تم إنشاء Word تجريبي (حجم: {len(docx_data)} بايت)")
        return docx_data
        
    except Exception as e:
        print(f"❌ فشل في إنشاء Word: {e}")
        return None

def test_document_info(translator, file_data, file_name):
    """اختبار الحصول على معلومات المستند"""
    print(f"\n🔍 اختبار معلومات المستند: {file_name}")
    
    try:
        info = translator.get_document_info(file_data, file_name)
        
        if 'error' in info:
            print(f"❌ خطأ في الحصول على المعلومات: {info['error']}")
            return False
        
        print(f"   📄 اسم الملف: {info['file_name']}")
        print(f"   📏 حجم الملف: {info['file_size']} بايت")
        print(f"   📄 عدد الصفحات: {info['pages']}")
        print(f"   📝 طول النص: {info['text_length']} حرف")
        print(f"   🖼️ يحتوي على صور: {'نعم' if info['has_images'] else 'لا'}")
        print(f"   📊 يحتوي على جداول: {'نعم' if info['has_tables'] else 'لا'}")
        
        print("✅ تم الحصول على معلومات المستند بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معلومات المستند: {e}")
        return False

def test_arabic_text_processing(translator):
    """اختبار معالجة النص العربي"""
    print("\n🔍 اختبار معالجة النص العربي...")
    
    test_texts = [
        "مرحبا بالعالم",
        "نظام ترجمة احترافي",
        "Mixed text: مرحبا English world",
        "Hello مرحبا World عالم",
        "هذا نص عربي طويل يحتوي على كلمات متعددة لاختبار النظام"
    ]
    
    try:
        for text in test_texts:
            print(f"   النص الأصلي: {text}")
            
            # كشف اللغة
            lang = translator.detect_language(text)
            print(f"   اللغة المكتشفة: {lang}")
            
            # إصلاح النص العربي
            fixed_text = translator.fix_arabic_text(text)
            print(f"   النص المصلح: {fixed_text}")
            print("   ---")
        
        print("✅ معالجة النص العربي تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في معالجة النص العربي: {e}")
        return False

def test_translation_functionality(translator, file_data, file_name, target_lang='ar'):
    """اختبار وظيفة الترجمة"""
    print(f"\n🔍 اختبار ترجمة {file_name} إلى {target_lang}...")
    
    try:
        # ترجمة المستند
        success, translated_data, message = translator.translate_document(
            file_data, file_name, target_lang
        )
        
        if success:
            print(f"✅ {message}")
            print(f"   حجم الملف المترجم: {len(translated_data)} بايت")
            
            # حفظ النتيجة للمراجعة
            output_name = f"translated_{target_lang}_{file_name}"
            with open(output_name, 'wb') as f:
                f.write(translated_data)
            print(f"   تم حفظ النتيجة في: {output_name}")
            
            return True
        else:
            print(f"❌ فشل في الترجمة: {message}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الترجمة: {e}")
        return False

def test_linux_compatibility():
    """اختبار التوافق مع Linux"""
    print("\n🔍 اختبار التوافق مع Linux...")
    
    try:
        import platform
        system = platform.system()
        print(f"   نظام التشغيل: {system}")
        
        # اختبار الخطوط
        try:
            import matplotlib.font_manager as fm
            fonts = fm.findSystemFonts()
            print(f"   عدد الخطوط المتوفرة: {len(fonts)}")
        except:
            print("   ⚠️ matplotlib غير متوفر لفحص الخطوط")
        
        # اختبار المجلدات المؤقتة
        temp_dir = tempfile.mkdtemp()
        if os.path.exists(temp_dir):
            print(f"   ✅ المجلد المؤقت: {temp_dir}")
            os.rmdir(temp_dir)
        
        # اختبار الذاكرة
        import psutil
        memory = psutil.virtual_memory()
        print(f"   الذاكرة المتوفرة: {memory.available // (1024*1024)} MB")
        
        print("✅ النظام متوافق مع Linux")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في اختبار Linux: {e}")
        return True  # ليس خطأ حرج

def main():
    """تشغيل جميع اختبارات النظام الاحترافي"""
    print("🚀 بدء اختبار النظام الاحترافي لترجمة المستندات")
    print("=" * 80)
    
    # اختبار المتطلبات
    if not test_system_requirements():
        print("❌ فشل في اختبار المتطلبات. يرجى تثبيت المكتبات المفقودة.")
        return False
    
    # اختبار الاستيراد
    success, translator = test_professional_translator_import()
    if not success:
        return False
    
    # اختبار التوافق مع Linux
    test_linux_compatibility()
    
    # اختبار معالجة النص العربي
    if not test_arabic_text_processing(translator):
        return False
    
    # إنشاء ملفات تجريبية
    pdf_data = create_test_pdf_with_arabic()
    docx_data = create_test_docx_with_arabic()
    
    if not pdf_data or not docx_data:
        print("❌ فشل في إنشاء الملفات التجريبية")
        return False
    
    # اختبار معلومات المستندات
    test_document_info(translator, pdf_data, "test.pdf")
    test_document_info(translator, docx_data, "test.docx")
    
    # اختبار الترجمة
    tests_passed = 0
    total_tests = 4
    
    # ترجمة PDF إلى العربية
    if test_translation_functionality(translator, pdf_data, "test.pdf", "ar"):
        tests_passed += 1
    
    # ترجمة PDF إلى الإنجليزية
    if test_translation_functionality(translator, pdf_data, "test.pdf", "en"):
        tests_passed += 1
    
    # ترجمة Word إلى العربية
    if test_translation_functionality(translator, docx_data, "test.docx", "ar"):
        tests_passed += 1
    
    # ترجمة Word إلى الإنجليزية
    if test_translation_functionality(translator, docx_data, "test.docx", "en"):
        tests_passed += 1
    
    print("\n" + "=" * 80)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبار ترجمة نجح")
    
    if tests_passed >= total_tests - 1:
        print("🎉 النظام الاحترافي يعمل بشكل ممتاز!")
        print("\n✨ المميزات المحققة:")
        print("   ✅ يعمل على Linux بدون مشاكل")
        print("   ✅ يحافظ على التنسيق والصور")
        print("   ✅ يدعم النص العربي RTL بشكل صحيح")
        print("   ✅ لا يعتمد على واجهة رسومية")
        print("   ✅ قابل للنشر على Docker/Hugging Face")
        print("   ✅ معالجة احترافية للـ PDF و Word")
        
        print("\n🎯 الملفات المنتجة:")
        print("   📄 translated_ar_test.pdf - PDF مترجم للعربية")
        print("   📄 translated_en_test.pdf - PDF مترجم للإنجليزية")
        print("   📝 translated_ar_test.docx - Word مترجم للعربية")
        print("   📝 translated_en_test.docx - Word مترجم للإنجليزية")
        
        return True
    else:
        print("⚠️ بعض اختبارات الترجمة فشلت")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
