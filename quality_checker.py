#!/usr/bin/env python3
"""
أداة فحص جودة ترجمة PDF
تقارن بين الملف الأصلي والمترجم وتقيم الجودة
"""

import os
import sys
import fitz
import json
from pathlib import Path
from typing import Dict, Any, Tuple
import difflib
from PIL import Image
import numpy as np

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ultimate_pdf_translator import advanced_logger

class PDFQualityChecker:
    """فاحص جودة ترجمة PDF"""
    
    def __init__(self):
        self.quality_metrics = {}
    
    def compare_pdfs(self, original_path: str, translated_path: str) -> Dict[str, Any]:
        """مقارنة شاملة بين PDF الأصلي والمترجم"""
        try:
            original_file = Path(original_path)
            translated_file = Path(translated_path)
            
            if not original_file.exists():
                return {"error": f"الملف الأصلي غير موجود: {original_path}"}
            
            if not translated_file.exists():
                return {"error": f"الملف المترجم غير موجود: {translated_path}"}
            
            # فتح الملفات
            original_doc = fitz.open(original_path)
            translated_doc = fitz.open(translated_path)
            
            # مقارنة أساسية
            basic_comparison = self._compare_basic_structure(original_doc, translated_doc)
            
            # مقارنة النصوص
            text_comparison = self._compare_text_content(original_doc, translated_doc)
            
            # مقارنة الصور
            image_comparison = self._compare_images(original_doc, translated_doc)
            
            # مقارنة التنسيق
            layout_comparison = self._compare_layout(original_doc, translated_doc)
            
            # حساب النتيجة الإجمالية
            overall_score = self._calculate_overall_score(
                basic_comparison, text_comparison, image_comparison, layout_comparison
            )
            
            # إغلاق الملفات
            original_doc.close()
            translated_doc.close()
            
            result = {
                'original_file': str(original_file),
                'translated_file': str(translated_file),
                'basic_comparison': basic_comparison,
                'text_comparison': text_comparison,
                'image_comparison': image_comparison,
                'layout_comparison': layout_comparison,
                'overall_score': overall_score,
                'quality_grade': self._get_quality_grade(overall_score)
            }
            
            advanced_logger.log_operation("QUALITY_CHECK", "SUCCESS", {
                "original": str(original_file),
                "translated": str(translated_file),
                "score": overall_score
            })
            
            return result
            
        except Exception as e:
            advanced_logger.log_operation("QUALITY_CHECK", "FAILED", {"error": str(e)})
            return {"error": f"خطأ في المقارنة: {str(e)}"}
    
    def _compare_basic_structure(self, original_doc, translated_doc) -> Dict[str, Any]:
        """مقارنة البنية الأساسية"""
        original_pages = len(original_doc)
        translated_pages = len(translated_doc)
        
        return {
            'original_pages': original_pages,
            'translated_pages': translated_pages,
            'pages_match': original_pages == translated_pages,
            'page_difference': abs(original_pages - translated_pages),
            'structure_score': 100 if original_pages == translated_pages else max(0, 100 - abs(original_pages - translated_pages) * 10)
        }
    
    def _compare_text_content(self, original_doc, translated_doc) -> Dict[str, Any]:
        """مقارنة محتوى النص"""
        original_text_total = ""
        translated_text_total = ""
        
        page_comparisons = []
        
        max_pages = min(len(original_doc), len(translated_doc))
        
        for page_num in range(max_pages):
            original_page = original_doc[page_num]
            translated_page = translated_doc[page_num]
            
            original_text = original_page.get_text().strip()
            translated_text = translated_page.get_text().strip()
            
            original_text_total += original_text
            translated_text_total += translated_text
            
            # مقارنة الصفحة
            page_comparison = {
                'page_number': page_num + 1,
                'original_length': len(original_text),
                'translated_length': len(translated_text),
                'has_original_text': len(original_text) > 0,
                'has_translated_text': len(translated_text) > 0,
                'text_preserved': len(translated_text) > 0 if len(original_text) > 0 else True
            }
            
            page_comparisons.append(page_comparison)
        
        # حساب إحصائيات عامة
        total_pages_with_original_text = sum(1 for p in page_comparisons if p['has_original_text'])
        total_pages_with_translated_text = sum(1 for p in page_comparisons if p['has_translated_text'])
        pages_with_preserved_text = sum(1 for p in page_comparisons if p['text_preserved'])
        
        text_preservation_rate = (pages_with_preserved_text / max(total_pages_with_original_text, 1)) * 100
        
        return {
            'original_text_length': len(original_text_total),
            'translated_text_length': len(translated_text_total),
            'pages_with_original_text': total_pages_with_original_text,
            'pages_with_translated_text': total_pages_with_translated_text,
            'text_preservation_rate': text_preservation_rate,
            'page_comparisons': page_comparisons,
            'text_score': text_preservation_rate
        }
    
    def _compare_images(self, original_doc, translated_doc) -> Dict[str, Any]:
        """مقارنة الصور"""
        original_images_total = 0
        translated_images_total = 0
        
        page_image_comparisons = []
        
        max_pages = min(len(original_doc), len(translated_doc))
        
        for page_num in range(max_pages):
            original_page = original_doc[page_num]
            translated_page = translated_doc[page_num]
            
            original_images = original_page.get_images()
            translated_images = translated_page.get_images()
            
            original_images_total += len(original_images)
            translated_images_total += len(translated_images)
            
            page_image_comparison = {
                'page_number': page_num + 1,
                'original_images': len(original_images),
                'translated_images': len(translated_images),
                'images_preserved': len(translated_images) >= len(original_images)
            }
            
            page_image_comparisons.append(page_image_comparison)
        
        # حساب معدل الحفاظ على الصور
        if original_images_total > 0:
            image_preservation_rate = min(100, (translated_images_total / original_images_total) * 100)
        else:
            image_preservation_rate = 100  # لا توجد صور للمقارنة
        
        return {
            'original_images_total': original_images_total,
            'translated_images_total': translated_images_total,
            'image_preservation_rate': image_preservation_rate,
            'page_image_comparisons': page_image_comparisons,
            'image_score': image_preservation_rate
        }
    
    def _compare_layout(self, original_doc, translated_doc) -> Dict[str, Any]:
        """مقارنة التخطيط والتنسيق"""
        layout_similarities = []
        
        max_pages = min(len(original_doc), len(translated_doc))
        
        for page_num in range(max_pages):
            original_page = original_doc[page_num]
            translated_page = translated_doc[page_num]
            
            # مقارنة أبعاد الصفحة
            original_rect = original_page.rect
            translated_rect = translated_page.rect
            
            size_similarity = self._calculate_size_similarity(original_rect, translated_rect)
            
            # مقارنة عدد كتل النص
            original_blocks = len(original_page.get_text("dict").get("blocks", []))
            translated_blocks = len(translated_page.get_text("dict").get("blocks", []))
            
            block_similarity = self._calculate_block_similarity(original_blocks, translated_blocks)
            
            page_layout_similarity = (size_similarity + block_similarity) / 2
            
            layout_similarities.append({
                'page_number': page_num + 1,
                'size_similarity': size_similarity,
                'block_similarity': block_similarity,
                'overall_similarity': page_layout_similarity
            })
        
        # حساب متوسط التشابه
        if layout_similarities:
            average_layout_similarity = sum(p['overall_similarity'] for p in layout_similarities) / len(layout_similarities)
        else:
            average_layout_similarity = 0
        
        return {
            'page_layout_similarities': layout_similarities,
            'average_layout_similarity': average_layout_similarity,
            'layout_score': average_layout_similarity
        }
    
    def _calculate_size_similarity(self, rect1, rect2) -> float:
        """حساب تشابه الأحجام"""
        try:
            area1 = rect1.width * rect1.height
            area2 = rect2.width * rect2.height
            
            if area1 == 0 and area2 == 0:
                return 100
            
            similarity = min(area1, area2) / max(area1, area2) * 100
            return similarity
        except:
            return 0
    
    def _calculate_block_similarity(self, blocks1, blocks2) -> float:
        """حساب تشابه عدد الكتل"""
        if blocks1 == 0 and blocks2 == 0:
            return 100
        
        if blocks1 == 0 or blocks2 == 0:
            return 0
        
        similarity = min(blocks1, blocks2) / max(blocks1, blocks2) * 100
        return similarity
    
    def _calculate_overall_score(self, basic, text, image, layout) -> float:
        """حساب النتيجة الإجمالية"""
        # أوزان مختلفة للمعايير
        weights = {
            'structure': 0.2,  # 20%
            'text': 0.4,       # 40%
            'image': 0.2,      # 20%
            'layout': 0.2      # 20%
        }
        
        structure_score = basic.get('structure_score', 0)
        text_score = text.get('text_score', 0)
        image_score = image.get('image_score', 0)
        layout_score = layout.get('layout_score', 0)
        
        overall_score = (
            structure_score * weights['structure'] +
            text_score * weights['text'] +
            image_score * weights['image'] +
            layout_score * weights['layout']
        )
        
        return round(overall_score, 2)
    
    def _get_quality_grade(self, score: float) -> str:
        """تحديد درجة الجودة"""
        if score >= 90:
            return "ممتاز"
        elif score >= 80:
            return "جيد جداً"
        elif score >= 70:
            return "جيد"
        elif score >= 60:
            return "مقبول"
        else:
            return "ضعيف"
    
    def generate_report(self, comparison_result: Dict[str, Any], output_path: str = None):
        """إنشاء تقرير مفصل"""
        if "error" in comparison_result:
            print(f"❌ خطأ: {comparison_result['error']}")
            return
        
        print("📊 تقرير جودة الترجمة")
        print("=" * 60)
        
        # معلومات أساسية
        print(f"📄 الملف الأصلي: {comparison_result['original_file']}")
        print(f"📄 الملف المترجم: {comparison_result['translated_file']}")
        print(f"🏆 النتيجة الإجمالية: {comparison_result['overall_score']:.2f}/100")
        print(f"📈 درجة الجودة: {comparison_result['quality_grade']}")
        
        print("\n📋 تفاصيل المقارنة:")
        
        # البنية الأساسية
        basic = comparison_result['basic_comparison']
        print(f"   📚 الصفحات: {basic['original_pages']} → {basic['translated_pages']} ({'✅' if basic['pages_match'] else '❌'})")
        
        # النصوص
        text = comparison_result['text_comparison']
        print(f"   📝 النصوص: {text['text_preservation_rate']:.1f}% محفوظة")
        print(f"   📏 طول النص: {text['original_text_length']} → {text['translated_text_length']}")
        
        # الصور
        image = comparison_result['image_comparison']
        print(f"   🖼️ الصور: {image['image_preservation_rate']:.1f}% محفوظة")
        print(f"   📊 عدد الصور: {image['original_images_total']} → {image['translated_images_total']}")
        
        # التخطيط
        layout = comparison_result['layout_comparison']
        print(f"   🎨 التخطيط: {layout['average_layout_similarity']:.1f}% متشابه")
        
        # حفظ التقرير
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(comparison_result, f, ensure_ascii=False, indent=2)
                print(f"\n💾 تم حفظ التقرير المفصل في: {output_path}")
            except Exception as e:
                print(f"\n❌ خطأ في حفظ التقرير: {e}")

def main():
    """الدالة الرئيسية لأداة فحص الجودة"""
    import argparse
    
    parser = argparse.ArgumentParser(description="أداة فحص جودة ترجمة PDF")
    parser.add_argument('original', help='مسار الملف الأصلي')
    parser.add_argument('translated', help='مسار الملف المترجم')
    parser.add_argument('-o', '--output', help='مسار حفظ التقرير (JSON)')
    
    args = parser.parse_args()
    
    checker = PDFQualityChecker()
    result = checker.compare_pdfs(args.original, args.translated)
    checker.generate_report(result, args.output)

if __name__ == "__main__":
    main()
