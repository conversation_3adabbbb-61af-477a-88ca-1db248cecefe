#!/usr/bin/env python3
"""
اختبار نظام التقدم في الترجمة الاحترافية
"""

import os
import sys
import time
import io
from pathlib import Path

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_large_test_pdf():
    """إنشاء PDF كبير لاختبار التقدم"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        output_buffer = io.BytesIO()
        c = canvas.Canvas(output_buffer, pagesize=A4)
        
        # إنشاء 5 صفحات مع نصوص متنوعة
        for page_num in range(5):
            c.setFont("Helvetica-Bold", 16)
            c.drawString(100, 750, f"Test Document - Page {page_num + 1}")
            
            c.setFont("Helvetica", 12)
            y_position = 700
            
            # إضافة نصوص متعددة في كل صفحة
            texts = [
                "This is a comprehensive test document for the professional translation system.",
                "The system is designed to work efficiently on Linux servers without GUI dependencies.",
                "It preserves formatting, images, tables, and handles Arabic RTL text correctly.",
                "This document contains multiple pages to test the progress tracking functionality.",
                "Each page has several text blocks that will be translated individually.",
                "The progress bar should show the current percentage of completion.",
                "Users can see exactly how much of the document has been processed.",
                "This provides a better user experience during long translation operations.",
                "The system handles both PDF and Word documents with equal efficiency.",
                "Arabic text is properly reshaped and displayed with correct RTL direction."
            ]
            
            for i, text in enumerate(texts):
                c.drawString(100, y_position - (i * 30), text)
            
            # إضافة نص عربي
            c.drawString(100, 300, f"هذا نص عربي في الصفحة {page_num + 1}")
            c.drawString(100, 270, "سيتم ترجمة هذا النص مع عرض التقدم")
            
            c.showPage()
        
        c.save()
        output_buffer.seek(0)
        
        return output_buffer.getvalue()
        
    except Exception as e:
        print(f"خطأ في إنشاء PDF: {e}")
        return None

def create_large_test_docx():
    """إنشاء Word كبير لاختبار التقدم"""
    try:
        from docx import Document
        
        doc = Document()
        
        # إضافة عنوان
        doc.add_heading('Test Document with Progress Tracking', 0)
        
        # إضافة فقرات متعددة
        paragraphs = [
            "This is a comprehensive test document for the professional translation system.",
            "The system is designed to work efficiently on Linux servers without GUI dependencies.",
            "It preserves formatting, images, tables, and handles Arabic RTL text correctly.",
            "This document contains multiple paragraphs to test the progress tracking functionality.",
            "Each paragraph will be translated individually with progress updates.",
            "The progress bar should show the current percentage of completion.",
            "Users can see exactly how much of the document has been processed.",
            "This provides a better user experience during long translation operations.",
            "The system handles both PDF and Word documents with equal efficiency.",
            "Arabic text is properly reshaped and displayed with correct RTL direction.",
            "هذا نص عربي لاختبار النظام",
            "سيتم ترجمة هذا النص مع عرض التقدم",
            "النظام يدعم النص العربي بشكل كامل",
            "يتم الحفاظ على التنسيق والاتجاه الصحيح",
            "This is another English paragraph for testing.",
            "The translation system works with mixed content.",
            "Both English and Arabic text are handled correctly.",
            "Progress tracking works for all types of content.",
            "The user interface shows real-time updates.",
            "This makes the translation process more transparent."
        ]
        
        for para_text in paragraphs:
            doc.add_paragraph(para_text)
        
        # إضافة جدول
        table = doc.add_table(rows=5, cols=3)
        table.style = 'Table Grid'
        
        for i in range(5):
            for j in range(3):
                cell_text = f"Table cell {i+1}-{j+1}: This is test content for translation."
                table.rows[i].cells[j].text = cell_text
        
        # حفظ في buffer
        output_buffer = io.BytesIO()
        doc.save(output_buffer)
        output_buffer.seek(0)
        
        return output_buffer.getvalue()
        
    except Exception as e:
        print(f"خطأ في إنشاء Word: {e}")
        return None

def test_progress_callback():
    """اختبار دالة التقدم"""
    print("🔍 اختبار نظام التقدم...")
    
    def progress_callback(percent, status_text):
        # محاكاة شريط التقدم
        progress_bar = "█" * (percent // 5) + "░" * (20 - (percent // 5))
        print(f"\r[{progress_bar}] {percent}% - {status_text}", end="", flush=True)
        time.sleep(0.1)  # محاكاة وقت المعالجة
    
    # محاكاة عملية ترجمة
    for i in range(0, 101, 10):
        progress_callback(i, f"معالجة العنصر {i//10 + 1}/11")
    
    print("\n✅ اختبار التقدم مكتمل")

def test_translation_with_progress():
    """اختبار الترجمة مع عرض التقدم"""
    print("\n🚀 اختبار الترجمة مع عرض التقدم")
    print("=" * 60)
    
    try:
        from professional_document_translator import translate_document_file
        
        # إنشاء ملفات اختبار
        print("📄 إنشاء ملفات اختبار...")
        pdf_data = create_large_test_pdf()
        docx_data = create_large_test_docx()
        
        if not pdf_data or not docx_data:
            print("❌ فشل في إنشاء ملفات الاختبار")
            return False
        
        print("✅ تم إنشاء ملفات الاختبار")
        
        # دالة التقدم مع عرض مرئي
        def visual_progress_callback(percent, status_text):
            progress_bar = "█" * (percent // 5) + "░" * (20 - (percent // 5))
            print(f"\r[{progress_bar}] {percent}% - {status_text}", end="", flush=True)
        
        # اختبار ترجمة PDF
        print("\n\n🔍 اختبار ترجمة PDF مع التقدم:")
        success, translated_pdf, message = translate_document_file(
            pdf_data, "large_test.pdf", "ar", visual_progress_callback
        )
        
        if success:
            print(f"\n✅ {message}")
            print(f"   حجم الملف المترجم: {len(translated_pdf)} بايت")
            
            # حفظ النتيجة
            with open("translated_progress_test.pdf", "wb") as f:
                f.write(translated_pdf)
            print("   تم حفظ النتيجة في: translated_progress_test.pdf")
        else:
            print(f"\n❌ فشل في ترجمة PDF: {message}")
            return False
        
        # اختبار ترجمة Word
        print("\n\n🔍 اختبار ترجمة Word مع التقدم:")
        success, translated_docx, message = translate_document_file(
            docx_data, "large_test.docx", "ar", visual_progress_callback
        )
        
        if success:
            print(f"\n✅ {message}")
            print(f"   حجم الملف المترجم: {len(translated_docx)} بايت")
            
            # حفظ النتيجة
            with open("translated_progress_test.docx", "wb") as f:
                f.write(translated_docx)
            print("   تم حفظ النتيجة في: translated_progress_test.docx")
        else:
            print(f"\n❌ فشل في ترجمة Word: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار الترجمة: {e}")
        return False

def simulate_telegram_progress():
    """محاكاة عرض التقدم في تليجرام"""
    print("\n📱 محاكاة عرض التقدم في تليجرام:")
    print("=" * 60)
    
    def telegram_progress_callback(percent, status_text):
        # محاكاة شريط التقدم كما سيظهر في تليجرام
        progress_bar = "█" * (percent // 5) + "░" * (20 - (percent // 5))
        full_text = f"{status_text}\n\n[{progress_bar}] {percent}%"
        
        # مسح الشاشة ومحاكاة تحديث الرسالة
        os.system('cls' if os.name == 'nt' else 'clear')
        print("📱 رسالة تليجرام:")
        print("-" * 40)
        print(full_text)
        print("-" * 40)
        time.sleep(0.5)  # محاكاة وقت المعالجة
    
    # محاكاة عملية ترجمة
    stages = [
        (0, "🔄 جاري ترجمة الملف بالنظام الاحترافي..."),
        (10, "📄 بدء معالجة PDF (5 صفحات)"),
        (25, "🔄 معالجة الصفحة 2/5 (25%)"),
        (45, "🔄 معالجة الصفحة 3/5 (45%)"),
        (65, "🔄 معالجة الصفحة 4/5 (65%)"),
        (85, "🔄 معالجة الصفحة 5/5 (85%)"),
        (100, "✅ تم الانتهاء من الترجمة - جاري حفظ الملف...")
    ]
    
    for percent, status in stages:
        telegram_progress_callback(percent, status)
    
    # رسالة النهاية
    os.system('cls' if os.name == 'nt' else 'clear')
    print("📱 رسالة تليجرام:")
    print("-" * 40)
    print("✅ تم ترجمة PDF بنجاح")
    print("\n📄 الملف المترجم: translated_document.pdf")
    print("✨ تم الترجمة باستخدام النظام الاحترافي")
    print("🔧 تم الحفاظ على التنسيق والصور")
    print("🌐 النص العربي مُصحح تلقائياً")
    print("-" * 40)

def main():
    """تشغيل جميع اختبارات التقدم"""
    print("🚀 اختبار نظام التقدم في الترجمة الاحترافية")
    print("=" * 80)
    
    # اختبار دالة التقدم الأساسية
    test_progress_callback()
    
    # محاكاة عرض التقدم في تليجرام
    simulate_telegram_progress()
    
    # اختبار الترجمة الفعلية مع التقدم
    success = test_translation_with_progress()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 جميع اختبارات التقدم نجحت!")
        print("\n✨ المميزات المحققة:")
        print("   ✅ عرض نسبة مئوية دقيقة للتقدم")
        print("   ✅ شريط تقدم مرئي في تليجرام")
        print("   ✅ رسائل حالة تفصيلية")
        print("   ✅ تحديث مباشر أثناء المعالجة")
        print("   ✅ عرض عدد الصفحات/العناصر المعالجة")
        
        print("\n🎯 الملفات المنتجة:")
        print("   📄 translated_progress_test.pdf")
        print("   📝 translated_progress_test.docx")
        
        return True
    else:
        print("⚠️ بعض اختبارات التقدم فشلت")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
