#!/usr/bin/env python3
"""
اختبار نظام الترجمة المخصص الجديد
"""

import os
import sys
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_translation_custom_button():
    """اختبار إنشاء زر ترجمة مخصص"""
    print("🔍 اختبار إنشاء زر ترجمة مخصص...")
    
    try:
        from keyboards.builder import create_button, process_button_action
        
        # إنشاء زر ترجمة مخصص
        button_id = create_button("🌍 ترجمة إلى العربية", "ترجمة", None, None, 1)
        
        if button_id:
            print(f"✅ تم إنشاء زر الترجمة المخصص: {button_id}")
            
            # اختبار معالجة الزر
            conn = sqlite3.connect('data/database.db')
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM buttons WHERE id = ?', (button_id,))
            button = cursor.fetchone()
            conn.close()
            
            if button:
                action_type, action_data = process_button_action(button, user_id=123)
                
                if action_type == "translation_custom":
                    print("✅ زر الترجمة يعمل كزر مخصص")
                    return True
                else:
                    print(f"❌ زر الترجمة يعطي نوع خاطئ: {action_type}")
                    return False
            else:
                print("❌ فشل في جلب بيانات الزر")
                return False
        else:
            print("❌ فشل في إنشاء زر الترجمة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار زر الترجمة المخصص: {str(e)}")
        return False

def test_translation_settings_functions():
    """اختبار دوال إعدادات الترجمة"""
    print("\n🔍 اختبار دوال إعدادات الترجمة...")
    
    try:
        from keyboards.builder import (
            save_translation_settings, get_translation_settings,
            get_translation_start_message, get_translation_target_language,
            get_default_translation_prompt
        )
        
        # اختبار حفظ الإعدادات
        test_button_id = 999
        save_translation_settings(
            button_id=test_button_id,
            api_keys='["test_key1", "test_key2"]',
            model="gemma-3n-e2b-it",
            target_language="ar",
            custom_prompt="Test prompt for Arabic translation",
            start_message="مرحباً! أرسل النص للترجمة إلى العربية"
        )
        
        print("✅ تم حفظ إعدادات الترجمة")
        
        # اختبار جلب الإعدادات
        settings = get_translation_settings(test_button_id)
        if settings:
            print("✅ تم جلب إعدادات الترجمة بنجاح")
            print(f"   - نوع الترجمة: {settings[2]}")
            print(f"   - النموذج: {settings[5]}")
        else:
            print("❌ فشل في جلب إعدادات الترجمة")
            return False
        
        # اختبار جلب رسالة البداية
        start_message = get_translation_start_message(test_button_id)
        if start_message:
            print(f"✅ رسالة البداية: {start_message[:50]}...")
        else:
            print("❌ فشل في جلب رسالة البداية")
        
        # اختبار جلب اللغة المستهدفة
        target_lang = get_translation_target_language(test_button_id)
        if target_lang:
            print(f"✅ اللغة المستهدفة: {target_lang}")
        else:
            print("❌ فشل في جلب اللغة المستهدفة")
        
        # اختبار البرومبت الافتراضي
        default_prompt_ar = get_default_translation_prompt("ar")
        default_prompt_en = get_default_translation_prompt("en")
        
        if default_prompt_ar and default_prompt_en:
            print("✅ البرومبتات الافتراضية متوفرة")
            print(f"   - العربية: {len(default_prompt_ar)} حرف")
            print(f"   - الإنجليزية: {len(default_prompt_en)} حرف")
        else:
            print("❌ فشل في جلب البرومبتات الافتراضية")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الإعدادات: {str(e)}")
        return False

def test_translation_system_integration():
    """اختبار تكامل نظام الترجمة"""
    print("\n🔍 اختبار تكامل نظام الترجمة...")
    
    try:
        from translation_system import translation_system
        from keyboards.builder import get_translation_settings
        
        # البحث عن زر ترجمة مُعد
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute('SELECT button_id FROM translation_settings ORDER BY button_id DESC LIMIT 1')
        result = cursor.fetchone()
        conn.close()
        
        if result:
            button_id = result[0]
            print(f"✅ تم العثور على زر ترجمة مُعد: {button_id}")
            
            # اختبار ترجمة نص تجريبي
            test_text = "Hello, this is a test message for translation."
            
            try:
                success, translated = translation_system.translate_text_ai(test_text, button_id, 123)
                
                if success:
                    print(f"✅ الترجمة بالذكاء الاصطناعي تعمل")
                    print(f"   النص الأصلي: {test_text}")
                    print(f"   الترجمة: {translated[:100]}...")
                else:
                    print(f"⚠️ فشل الذكاء الاصطناعي، سيتم التبديل للترجمة العادية: {translated}")
                    
                    # اختبار الترجمة العادية
                    success_fallback, translated_fallback = translation_system.translate_text_fallback(
                        test_text, 'to_arabic', 123
                    )
                    
                    if success_fallback:
                        print("✅ الترجمة العادية (البديلة) تعمل")
                    else:
                        print(f"❌ فشل في الترجمة العادية: {translated_fallback}")
                        return False
                
                return True
                
            except Exception as e:
                print(f"❌ خطأ في اختبار الترجمة: {str(e)}")
                return False
        else:
            print("⚠️ لا يوجد زر ترجمة مُعد للاختبار")
            return True  # لا نعتبر هذا فشل
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        return False

def test_database_structure():
    """اختبار بنية قاعدة البيانات للترجمة المخصصة"""
    print("\n🔍 اختبار بنية قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # التحقق من الجداول المطلوبة
        required_tables = ['translation_settings', 'translation_global_settings']
        
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
                conn.close()
                return False
        
        # التحقق من الأعمدة في جدول translation_settings
        cursor.execute("PRAGMA table_info(translation_settings)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = [
            'id', 'button_id', 'translation_type', 'use_ai', 
            'api_keys', 'model', 'custom_prompt', 'ai_cost', 'normal_cost'
        ]
        
        for column in required_columns:
            if column in columns:
                print(f"✅ عمود {column} موجود")
            else:
                print(f"❌ عمود {column} مفقود")
                conn.close()
                return False
        
        # التحقق من البيانات
        cursor.execute('SELECT COUNT(*) FROM translation_settings')
        settings_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM translation_global_settings')
        global_count = cursor.fetchone()[0]
        
        print(f"✅ عدد إعدادات الترجمة: {settings_count}")
        print(f"✅ عدد الإعدادات العامة: {global_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {str(e)}")
        return False

def cleanup_test_data():
    """تنظيف بيانات الاختبار"""
    print("\n🧹 تنظيف بيانات الاختبار...")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # حذف إعدادات الاختبار
        cursor.execute('DELETE FROM translation_settings WHERE button_id = 999')
        cursor.execute('DELETE FROM translation_global_settings WHERE key LIKE "%_999"')
        
        # حذف أزرار الاختبار
        cursor.execute('DELETE FROM buttons WHERE label LIKE "%ترجمة%" AND created_by = 1')
        
        conn.commit()
        conn.close()
        
        print("✅ تم تنظيف بيانات الاختبار")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات النظام المخصص"""
    print("🚀 بدء اختبار نظام الترجمة المخصص")
    print("=" * 50)
    
    tests = [
        ("بنية قاعدة البيانات", test_database_structure),
        ("زر الترجمة المخصص", test_translation_custom_button),
        ("دوال إعدادات الترجمة", test_translation_settings_functions),
        ("تكامل نظام الترجمة", test_translation_system_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    # تنظيف البيانات
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات النظام المخصص نجحت!")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
