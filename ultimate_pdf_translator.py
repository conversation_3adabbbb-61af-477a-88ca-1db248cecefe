"""
النظام النهائي لترجمة PDF - حل شامل ونهائي
يحل جميع مشاكل النص العربي مع أعلى معايير الجودة
"""

import os
import io
import sys
import json
import logging
import tempfile
import traceback
from pathlib import Path
from typing import Tuple, Optional, Dict, Any, List
from datetime import datetime
import fitz  # PyMuPDF
from docx import Document
import arabic_reshaper
from bidi.algorithm import get_display
from deep_translator import GoogleTranslator
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import requests
import zipfile
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib

# إعداد نظام التسجيل المتقدم
class AdvancedLogger:
    """نظام تسجيل متقدم لتتبع جميع العمليات"""
    
    def __init__(self, log_file="pdf_translation.log"):
        self.log_file = log_file
        self.setup_logging()
    
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        # إنشاء logger مخصص
        self.logger = logging.getLogger('PDFTranslator')
        self.logger.setLevel(logging.DEBUG)
        
        # إنشاء formatter
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # إضافة handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_operation(self, operation, status, details=None, file_info=None):
        """تسجيل عملية مع تفاصيل كاملة"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation,
            'status': status,
            'details': details or {},
            'file_info': file_info or {}
        }
        
        if status == 'SUCCESS':
            self.logger.info(f"✅ {operation}: {json.dumps(log_entry, ensure_ascii=False)}")
        elif status == 'FAILED':
            self.logger.error(f"❌ {operation}: {json.dumps(log_entry, ensure_ascii=False)}")
        elif status == 'WARNING':
            self.logger.warning(f"⚠️ {operation}: {json.dumps(log_entry, ensure_ascii=False)}")
        else:
            self.logger.debug(f"🔍 {operation}: {json.dumps(log_entry, ensure_ascii=False)}")

# إنشاء logger عام
advanced_logger = AdvancedLogger()

class ArabicFontManager:
    """مدير الخطوط العربية المتقدم"""
    
    def __init__(self):
        self.fonts_dir = Path("arabic_fonts")
        self.fonts_dir.mkdir(exist_ok=True)
        self.available_fonts = {}
        self.download_arabic_fonts()
        self.scan_system_fonts()
    
    def download_arabic_fonts(self):
        """تحميل خطوط عربية عالية الجودة"""
        advanced_logger.log_operation("FONT_DOWNLOAD", "STARTED", {"action": "downloading_arabic_fonts"})
        
        # قائمة الخطوط العربية المجانية عالية الجودة
        font_urls = {
            "NotoSansArabic-Regular.ttf": "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansArabic/NotoSansArabic-Regular.ttf",
            "NotoNaskhArabic-Regular.ttf": "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoNaskhArabic/NotoNaskhArabic-Regular.ttf",
            "Amiri-Regular.ttf": "https://github.com/aliftype/amiri/releases/download/0.117/Amiri-0.117.zip",
            "Scheherazade-Regular.ttf": "https://github.com/silnrsi/font-scheherazade/releases/download/v3.300/ScheherazadeNew-3.300.zip"
        }
        
        for font_name, url in font_urls.items():
            font_path = self.fonts_dir / font_name
            
            if not font_path.exists():
                try:
                    advanced_logger.log_operation("FONT_DOWNLOAD", "PROGRESS", {"font": font_name, "url": url})
                    
                    if url.endswith('.zip'):
                        self._download_and_extract_font(url, font_name)
                    else:
                        self._download_direct_font(url, font_path)
                    
                    advanced_logger.log_operation("FONT_DOWNLOAD", "SUCCESS", {"font": font_name})
                except Exception as e:
                    advanced_logger.log_operation("FONT_DOWNLOAD", "FAILED", {"font": font_name, "error": str(e)})
    
    def _download_direct_font(self, url, font_path):
        """تحميل خط مباشر"""
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        with open(font_path, 'wb') as f:
            f.write(response.content)
    
    def _download_and_extract_font(self, url, font_name):
        """تحميل واستخراج خط من ملف مضغوط"""
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # حفظ الملف المضغوط مؤقتاً
        zip_path = self.fonts_dir / f"temp_{font_name}.zip"
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        # استخراج الخط
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            for file_info in zip_ref.filelist:
                if file_info.filename.endswith('.ttf') and 'Regular' in file_info.filename:
                    # استخراج أول خط Regular موجود
                    with zip_ref.open(file_info) as source, open(self.fonts_dir / font_name, 'wb') as target:
                        target.write(source.read())
                    break
        
        # حذف الملف المضغوط
        zip_path.unlink()
    
    def scan_system_fonts(self):
        """فحص خطوط النظام"""
        system_font_paths = [
            "/usr/share/fonts/truetype/noto/",
            "/usr/share/fonts/truetype/dejavu/",
            "/usr/share/fonts/truetype/liberation/",
            "/System/Library/Fonts/",
            "C:/Windows/Fonts/",
            str(self.fonts_dir)
        ]
        
        for font_dir in system_font_paths:
            if os.path.exists(font_dir):
                for font_file in Path(font_dir).glob("*.ttf"):
                    if any(keyword in font_file.name.lower() for keyword in ['arabic', 'noto', 'amiri', 'scheherazade']):
                        self.available_fonts[font_file.name] = str(font_file)
        
        advanced_logger.log_operation("FONT_SCAN", "SUCCESS", {"fonts_found": len(self.available_fonts), "fonts": list(self.available_fonts.keys())})
    
    def get_best_arabic_font(self):
        """الحصول على أفضل خط عربي متوفر"""
        # ترتيب الأولوية
        priority_fonts = [
            "NotoNaskhArabic-Regular.ttf",
            "NotoSansArabic-Regular.ttf", 
            "Amiri-Regular.ttf",
            "Scheherazade-Regular.ttf"
        ]
        
        for font_name in priority_fonts:
            if font_name in self.available_fonts:
                return self.available_fonts[font_name]
        
        # إذا لم يوجد، استخدم أي خط عربي متوفر
        for font_name, font_path in self.available_fonts.items():
            if 'arabic' in font_name.lower():
                return font_path
        
        # كحل أخير، إنشاء خط افتراضي
        return self._create_fallback_font()
    
    def _create_fallback_font(self):
        """إنشاء خط احتياطي"""
        try:
            # محاولة استخدام خط النظام الافتراضي
            from matplotlib import font_manager
            fonts = font_manager.findSystemFonts()
            for font in fonts:
                if any(keyword in font.lower() for keyword in ['arial', 'dejavu', 'liberation']):
                    return font
        except:
            pass
        
        return None

class TextToImageRenderer:
    """محول النص إلى صورة عالية الجودة"""
    
    def __init__(self, font_manager):
        self.font_manager = font_manager
        self.dpi = 300  # دقة عالية
    
    def render_text_as_image(self, text, font_size, bbox, target_lang='ar'):
        """تحويل النص إلى صورة عالية الجودة"""
        try:
            # حساب أبعاد الصورة
            width = int(bbox[2] - bbox[0])
            height = int(bbox[3] - bbox[1])
            
            if width <= 0 or height <= 0:
                return None
            
            # إنشاء صورة بدقة عالية
            scale_factor = self.dpi / 72  # تحويل من 72 DPI إلى 300 DPI
            img_width = int(width * scale_factor)
            img_height = int(height * scale_factor)
            
            # إنشاء صورة شفافة
            img = Image.new('RGBA', (img_width, img_height), (255, 255, 255, 0))
            draw = ImageDraw.Draw(img)
            
            # تحديد الخط
            font_path = self.font_manager.get_best_arabic_font()
            if font_path and os.path.exists(font_path):
                font = ImageFont.truetype(font_path, int(font_size * scale_factor))
            else:
                # استخدام خط افتراضي
                try:
                    font = ImageFont.load_default()
                except:
                    return None
            
            # إصلاح النص العربي
            if target_lang == 'ar':
                text = self._fix_arabic_text(text)
            
            # حساب موقع النص
            if target_lang == 'ar':
                # للعربية - محاذاة يمين
                text_x = img_width - 10
                anchor = "ra"  # right-aligned
            else:
                # للإنجليزية - محاذاة يسار
                text_x = 10
                anchor = "la"  # left-aligned
            
            text_y = img_height // 2
            
            # رسم النص
            draw.text(
                (text_x, text_y),
                text,
                font=font,
                fill=(0, 0, 0, 255),  # أسود
                anchor=anchor
            )
            
            # تحويل إلى bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG', dpi=(self.dpi, self.dpi))
            img_buffer.seek(0)
            
            advanced_logger.log_operation("TEXT_TO_IMAGE", "SUCCESS", {
                "text_length": len(text),
                "image_size": f"{img_width}x{img_height}",
                "font_used": font_path or "default"
            })
            
            return img_buffer.getvalue()
            
        except Exception as e:
            advanced_logger.log_operation("TEXT_TO_IMAGE", "FAILED", {"error": str(e), "text": text[:50]})
            return None
    
    def _fix_arabic_text(self, text):
        """إصلاح النص العربي للعرض الصحيح"""
        try:
            # إعادة تشكيل الأحرف العربية
            reshaped_text = arabic_reshaper.reshape(text)
            # إصلاح اتجاه النص
            final_text = get_display(reshaped_text)
            return final_text
        except Exception as e:
            advanced_logger.log_operation("ARABIC_FIX", "FAILED", {"error": str(e), "text": text[:50]})
            return text

class PDFAnalyzer:
    """محلل PDF متقدم"""
    
    def __init__(self):
        pass
    
    def analyze_pdf_structure(self, pdf_data):
        """تحليل شامل لبنية PDF"""
        try:
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            
            analysis = {
                'total_pages': len(doc),
                'has_text': False,
                'has_images': False,
                'has_embedded_fonts': False,
                'text_extractable': False,
                'complexity_score': 0,
                'pages_analysis': []
            }
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # تحليل النص
                text = page.get_text()
                text_dict = page.get_text("dict")
                
                # تحليل الصور
                images = page.get_images()
                
                # تحليل الخطوط
                fonts = page.get_fonts()
                
                page_analysis = {
                    'page_number': page_num + 1,
                    'text_length': len(text),
                    'has_text': len(text.strip()) > 0,
                    'images_count': len(images),
                    'fonts_count': len(fonts),
                    'text_blocks': len(text_dict.get('blocks', [])),
                    'extractable_text': len(text.strip()) > 10
                }
                
                analysis['pages_analysis'].append(page_analysis)
                
                # تحديث التحليل العام
                if page_analysis['has_text']:
                    analysis['has_text'] = True
                if page_analysis['extractable_text']:
                    analysis['text_extractable'] = True
                if page_analysis['images_count'] > 0:
                    analysis['has_images'] = True
                if page_analysis['fonts_count'] > 0:
                    analysis['has_embedded_fonts'] = True
                
                # حساب درجة التعقيد
                complexity = 0
                complexity += min(page_analysis['text_blocks'], 10)  # عدد كتل النص
                complexity += min(page_analysis['images_count'], 5)  # عدد الصور
                complexity += min(page_analysis['fonts_count'], 3)   # عدد الخطوط
                analysis['complexity_score'] += complexity
            
            doc.close()
            
            # تحديد نوع PDF
            if analysis['text_extractable']:
                analysis['pdf_type'] = 'text_based'
            elif analysis['has_images'] and not analysis['has_text']:
                analysis['pdf_type'] = 'image_only'
            elif analysis['has_text'] and analysis['has_images']:
                analysis['pdf_type'] = 'mixed'
            else:
                analysis['pdf_type'] = 'unknown'
            
            advanced_logger.log_operation("PDF_ANALYSIS", "SUCCESS", analysis)
            
            return analysis
            
        except Exception as e:
            advanced_logger.log_operation("PDF_ANALYSIS", "FAILED", {"error": str(e)})
            return None

class IntelligentMethodSelector:
    """محدد الطريقة الذكي"""
    
    def __init__(self):
        pass
    
    def select_best_method(self, pdf_analysis):
        """اختيار أفضل طريقة ترجمة حسب تحليل PDF"""
        if not pdf_analysis:
            return "text_to_image"  # الطريقة الأكثر أماناً
        
        pdf_type = pdf_analysis.get('pdf_type', 'unknown')
        complexity = pdf_analysis.get('complexity_score', 0)
        has_embedded_fonts = pdf_analysis.get('has_embedded_fonts', False)
        text_extractable = pdf_analysis.get('text_extractable', False)
        
        # قواعد اختيار الطريقة
        if pdf_type == 'image_only':
            method = "ocr_overlay"
        elif pdf_type == 'text_based' and complexity < 20 and has_embedded_fonts:
            method = "embedded_fonts"
        elif pdf_type == 'mixed' or complexity > 30:
            method = "text_to_image"
        elif not text_extractable:
            method = "pdf_reconstruction"
        else:
            method = "text_to_image"  # الطريقة الافتراضية الأكثر أماناً
        
        advanced_logger.log_operation("METHOD_SELECTION", "SUCCESS", {
            "selected_method": method,
            "pdf_type": pdf_type,
            "complexity": complexity,
            "reasoning": f"Selected {method} based on PDF analysis"
        })
        
        return method

class UltimatePDFTranslator:
    """النظام النهائي لترجمة PDF"""

    def __init__(self):
        self.font_manager = ArabicFontManager()
        self.text_renderer = TextToImageRenderer(self.font_manager)
        self.pdf_analyzer = PDFAnalyzer()
        self.method_selector = IntelligentMethodSelector()
        self.translation_cache = {}
        self.temp_dir = tempfile.mkdtemp()

        advanced_logger.log_operation("SYSTEM_INIT", "SUCCESS", {
            "temp_dir": self.temp_dir,
            "available_fonts": len(self.font_manager.available_fonts)
        })

    def translate_pdf(self, pdf_data, target_lang='ar', progress_callback=None):
        """الدالة الرئيسية لترجمة PDF"""
        try:
            # تحليل PDF
            if progress_callback:
                progress_callback(5, "🔍 تحليل بنية PDF...")

            analysis = self.pdf_analyzer.analyze_pdf_structure(pdf_data)
            if not analysis:
                return False, b"", "فشل في تحليل PDF"

            # اختيار أفضل طريقة
            if progress_callback:
                progress_callback(10, "🧠 اختيار أفضل طريقة ترجمة...")

            method = self.method_selector.select_best_method(analysis)

            # تطبيق الطريقة المختارة مع fallback
            methods_to_try = [method]

            # إضافة طرق احتياطية
            all_methods = ["text_to_image", "embedded_fonts", "pdf_reconstruction", "ocr_overlay"]
            for backup_method in all_methods:
                if backup_method not in methods_to_try:
                    methods_to_try.append(backup_method)

            last_error = ""

            for attempt, current_method in enumerate(methods_to_try):
                try:
                    if progress_callback:
                        progress_callback(15 + attempt * 5, f"🔄 محاولة الطريقة: {current_method}")

                    advanced_logger.log_operation("TRANSLATION_ATTEMPT", "STARTED", {
                        "method": current_method,
                        "attempt": attempt + 1,
                        "total_methods": len(methods_to_try)
                    })

                    if current_method == "text_to_image":
                        success, result, message = self._translate_with_text_to_image(pdf_data, target_lang, progress_callback)
                    elif current_method == "embedded_fonts":
                        success, result, message = self._translate_with_embedded_fonts(pdf_data, target_lang, progress_callback)
                    elif current_method == "pdf_reconstruction":
                        success, result, message = self._translate_with_reconstruction(pdf_data, target_lang, progress_callback)
                    elif current_method == "ocr_overlay":
                        success, result, message = self._translate_with_ocr_overlay(pdf_data, target_lang, progress_callback)
                    else:
                        continue

                    if success:
                        advanced_logger.log_operation("TRANSLATION_SUCCESS", "SUCCESS", {
                            "method": current_method,
                            "attempt": attempt + 1,
                            "result_size": len(result)
                        })
                        return True, result, f"تم بنجاح باستخدام {current_method}: {message}"
                    else:
                        last_error = message
                        advanced_logger.log_operation("TRANSLATION_ATTEMPT", "FAILED", {
                            "method": current_method,
                            "error": message
                        })

                except Exception as e:
                    last_error = str(e)
                    advanced_logger.log_operation("TRANSLATION_ATTEMPT", "FAILED", {
                        "method": current_method,
                        "error": str(e),
                        "traceback": traceback.format_exc()
                    })

            # إذا فشلت جميع الطرق
            advanced_logger.log_operation("TRANSLATION_COMPLETE_FAILURE", "FAILED", {
                "methods_tried": methods_to_try,
                "last_error": last_error
            })

            return False, b"", f"فشل في جميع طرق الترجمة. آخر خطأ: {last_error}"

        except Exception as e:
            advanced_logger.log_operation("TRANSLATION_CRITICAL_ERROR", "FAILED", {
                "error": str(e),
                "traceback": traceback.format_exc()
            })
            return False, b"", f"خطأ حرج في النظام: {str(e)}"

    def _translate_with_text_to_image(self, pdf_data, target_lang, progress_callback):
        """ترجمة باستخدام تحويل النص إلى صورة"""
        try:
            advanced_logger.log_operation("TEXT_TO_IMAGE_METHOD", "STARTED")

            doc = fitz.open(stream=pdf_data, filetype="pdf")
            total_pages = len(doc)

            successful_replacements = 0
            failed_replacements = 0

            for page_num in range(total_pages):
                page = doc[page_num]

                if progress_callback:
                    progress = 30 + int((page_num / total_pages) * 60)
                    progress_callback(progress, f"🖼️ معالجة الصفحة {page_num + 1}/{total_pages} (Text-to-Image)")

                # استخراج النصوص
                text_dict = page.get_text("dict")

                for block in text_dict.get("blocks", []):
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                original_text = span["text"].strip()

                                if len(original_text) > 1:
                                    # ترجمة النص
                                    translated_text = self._translate_text_cached(original_text, target_lang)

                                    # تحويل النص إلى صورة
                                    bbox = span["bbox"]
                                    font_size = span["size"]

                                    img_data = self.text_renderer.render_text_as_image(
                                        translated_text, font_size, bbox, target_lang
                                    )

                                    if img_data:
                                        # حذف النص الأصلي
                                        rect = fitz.Rect(bbox)
                                        page.add_redact_annot(rect)

                                        # إدراج الصورة
                                        try:
                                            page.insert_image(rect, stream=img_data)
                                            successful_replacements += 1

                                            advanced_logger.log_operation("TEXT_REPLACEMENT", "SUCCESS", {
                                                "page": page_num + 1,
                                                "original": original_text[:50],
                                                "translated": translated_text[:50],
                                                "method": "image"
                                            })
                                        except Exception as e:
                                            failed_replacements += 1
                                            advanced_logger.log_operation("TEXT_REPLACEMENT", "FAILED", {
                                                "page": page_num + 1,
                                                "text": original_text[:50],
                                                "error": str(e)
                                            })
                                    else:
                                        failed_replacements += 1

                # تطبيق التغييرات
                page.apply_redactions()

            # حفظ النتيجة
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            doc.close()

            output_buffer.seek(0)
            result_data = output_buffer.getvalue()

            success_rate = (successful_replacements / (successful_replacements + failed_replacements)) * 100 if (successful_replacements + failed_replacements) > 0 else 0

            advanced_logger.log_operation("TEXT_TO_IMAGE_METHOD", "COMPLETED", {
                "successful_replacements": successful_replacements,
                "failed_replacements": failed_replacements,
                "success_rate": success_rate
            })

            if success_rate > 50:  # نعتبر النجاح إذا كان أكثر من 50%
                return True, result_data, f"معدل النجاح: {success_rate:.1f}%"
            else:
                return False, b"", f"معدل نجاح منخفض: {success_rate:.1f}%"

        except Exception as e:
            advanced_logger.log_operation("TEXT_TO_IMAGE_METHOD", "FAILED", {"error": str(e)})
            return False, b"", f"خطأ في طريقة Text-to-Image: {str(e)}"

    def _translate_with_embedded_fonts(self, pdf_data, target_lang, progress_callback):
        """ترجمة باستخدام الخطوط المدمجة"""
        try:
            advanced_logger.log_operation("EMBEDDED_FONTS_METHOD", "STARTED")

            doc = fitz.open(stream=pdf_data, filetype="pdf")

            # محاولة دمج خط عربي
            arabic_font_path = self.font_manager.get_best_arabic_font()
            if not arabic_font_path or not os.path.exists(arabic_font_path):
                return False, b"", "لا يوجد خط عربي متوفر"

            # قراءة الخط
            with open(arabic_font_path, 'rb') as font_file:
                font_buffer = font_file.read()

            # دمج الخط في PDF
            font_name = "ArabicFont"
            try:
                doc.insert_font(fontname=font_name, fontbuffer=font_buffer)
            except Exception as e:
                return False, b"", f"فشل في دمج الخط: {str(e)}"

            total_pages = len(doc)
            successful_replacements = 0
            failed_replacements = 0

            for page_num in range(total_pages):
                page = doc[page_num]

                if progress_callback:
                    progress = 30 + int((page_num / total_pages) * 60)
                    progress_callback(progress, f"🔤 معالجة الصفحة {page_num + 1}/{total_pages} (Embedded Fonts)")

                # استخراج ومعالجة النصوص
                text_dict = page.get_text("dict")

                for block in text_dict.get("blocks", []):
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                original_text = span["text"].strip()

                                if len(original_text) > 1:
                                    # ترجمة النص
                                    translated_text = self._translate_text_cached(original_text, target_lang)

                                    # إصلاح النص العربي
                                    if target_lang == 'ar':
                                        translated_text = self._fix_arabic_text(translated_text)

                                    bbox = span["bbox"]
                                    font_size = span["size"]

                                    # حذف النص الأصلي
                                    rect = fitz.Rect(bbox)
                                    page.add_redact_annot(rect)

                                    # إدراج النص الجديد
                                    try:
                                        x, y = bbox[0], bbox[1] + font_size * 0.8

                                        if target_lang == 'ar':
                                            # للعربية - من اليمين
                                            x = bbox[2] - 5

                                        page.insert_text(
                                            (x, y),
                                            translated_text,
                                            fontname=font_name,
                                            fontsize=font_size,
                                            color=(0, 0, 0)
                                        )

                                        successful_replacements += 1

                                        advanced_logger.log_operation("TEXT_REPLACEMENT", "SUCCESS", {
                                            "page": page_num + 1,
                                            "original": original_text[:50],
                                            "translated": translated_text[:50],
                                            "method": "embedded_font"
                                        })

                                    except Exception as e:
                                        failed_replacements += 1
                                        advanced_logger.log_operation("TEXT_REPLACEMENT", "FAILED", {
                                            "page": page_num + 1,
                                            "text": original_text[:50],
                                            "error": str(e)
                                        })

                # تطبيق التغييرات
                page.apply_redactions()

            # حفظ النتيجة
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            doc.close()

            output_buffer.seek(0)
            result_data = output_buffer.getvalue()

            success_rate = (successful_replacements / (successful_replacements + failed_replacements)) * 100 if (successful_replacements + failed_replacements) > 0 else 0

            advanced_logger.log_operation("EMBEDDED_FONTS_METHOD", "COMPLETED", {
                "successful_replacements": successful_replacements,
                "failed_replacements": failed_replacements,
                "success_rate": success_rate
            })

            if success_rate > 70:  # معيار أعلى للخطوط المدمجة
                return True, result_data, f"معدل النجاح: {success_rate:.1f}%"
            else:
                return False, b"", f"معدل نجاح منخفض: {success_rate:.1f}%"

        except Exception as e:
            advanced_logger.log_operation("EMBEDDED_FONTS_METHOD", "FAILED", {"error": str(e)})
            return False, b"", f"خطأ في طريقة Embedded Fonts: {str(e)}"

    def _translate_with_reconstruction(self, pdf_data, target_lang, progress_callback):
        """ترجمة بإعادة بناء PDF من الصفر"""
        try:
            advanced_logger.log_operation("PDF_RECONSTRUCTION_METHOD", "STARTED")

            # تحويل PDF إلى Word أولاً
            if progress_callback:
                progress_callback(35, "🔄 تحويل PDF إلى Word...")

            from pdf2docx import Converter

            # حفظ PDF مؤقتاً
            pdf_path = os.path.join(self.temp_dir, "temp_input.pdf")
            docx_path = os.path.join(self.temp_dir, "temp_converted.docx")

            with open(pdf_path, 'wb') as f:
                f.write(pdf_data)

            # التحويل
            cv = Converter(pdf_path)
            cv.convert(docx_path, start=0, end=None)
            cv.close()

            if progress_callback:
                progress_callback(50, "📝 ترجمة المستند...")

            # قراءة وترجمة Word
            with open(docx_path, 'rb') as f:
                docx_data = f.read()

            # ترجمة Word
            success, translated_docx = self._translate_word_document(docx_data, target_lang, progress_callback)

            if not success:
                return False, b"", "فشل في ترجمة المستند"

            if progress_callback:
                progress_callback(80, "📄 تحويل إلى PDF...")

            # تحويل Word المترجم إلى PDF
            translated_docx_path = os.path.join(self.temp_dir, "temp_translated.docx")
            with open(translated_docx_path, 'wb') as f:
                f.write(translated_docx)

            # استخدام LibreOffice للتحويل
            import subprocess
            try:
                result = subprocess.run([
                    'libreoffice', '--headless', '--convert-to', 'pdf',
                    '--outdir', self.temp_dir, translated_docx_path
                ], check=True, capture_output=True, timeout=120)

                # البحث عن ملف PDF الناتج
                output_pdf_path = None
                for file in os.listdir(self.temp_dir):
                    if file.endswith('.pdf') and 'temp_translated' in file:
                        output_pdf_path = os.path.join(self.temp_dir, file)
                        break

                if output_pdf_path and os.path.exists(output_pdf_path):
                    with open(output_pdf_path, 'rb') as f:
                        result_data = f.read()

                    # تنظيف الملفات المؤقتة
                    for temp_file in [pdf_path, docx_path, translated_docx_path, output_pdf_path]:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)

                    advanced_logger.log_operation("PDF_RECONSTRUCTION_METHOD", "SUCCESS", {
                        "result_size": len(result_data)
                    })

                    return True, result_data, "تم بنجاح عبر إعادة البناء"
                else:
                    return False, b"", "فشل في إنشاء PDF النهائي"

            except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
                # إذا لم يكن LibreOffice متوفراً، أرجع Word المترجم
                advanced_logger.log_operation("PDF_RECONSTRUCTION_METHOD", "PARTIAL_SUCCESS", {
                    "note": "LibreOffice not available, returning Word document"
                })
                return True, translated_docx, "تم الترجمة (ملف Word)"

        except Exception as e:
            advanced_logger.log_operation("PDF_RECONSTRUCTION_METHOD", "FAILED", {"error": str(e)})
            return False, b"", f"خطأ في إعادة البناء: {str(e)}"

    def _translate_with_ocr_overlay(self, pdf_data, target_lang, progress_callback):
        """ترجمة باستخدام OCR وطبقة نص"""
        try:
            advanced_logger.log_operation("OCR_OVERLAY_METHOD", "STARTED")

            # هذه الطريقة للملفات التي تحتوي على صور فقط
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            total_pages = len(doc)

            for page_num in range(total_pages):
                page = doc[page_num]

                if progress_callback:
                    progress = 30 + int((page_num / total_pages) * 60)
                    progress_callback(progress, f"👁️ معالجة الصفحة {page_num + 1}/{total_pages} (OCR)")

                # تحويل الصفحة إلى صورة
                mat = fitz.Matrix(2.0, 2.0)  # دقة عالية
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                # استخدام OCR لاستخراج النص (يتطلب تطبيق OCR هنا)
                # هذا مكان لتطبيق PaddleOCR أو Tesseract

                # للآن، سنعتبر هذه الطريقة غير متوفرة
                advanced_logger.log_operation("OCR_OVERLAY_METHOD", "NOT_IMPLEMENTED", {
                    "note": "OCR functionality needs to be implemented"
                })

                break

            doc.close()
            return False, b"", "طريقة OCR غير مطبقة بعد"

        except Exception as e:
            advanced_logger.log_operation("OCR_OVERLAY_METHOD", "FAILED", {"error": str(e)})
            return False, b"", f"خطأ في طريقة OCR: {str(e)}"

    def _translate_word_document(self, docx_data, target_lang, progress_callback):
        """ترجمة مستند Word"""
        try:
            doc = Document(io.BytesIO(docx_data))

            # جمع جميع الفقرات
            all_paragraphs = [p for p in doc.paragraphs if p.text.strip()]

            # معالجة الفقرات
            for i, paragraph in enumerate(all_paragraphs):
                if progress_callback:
                    progress = 55 + int((i / len(all_paragraphs)) * 20)
                    progress_callback(progress, f"📝 ترجمة فقرة {i+1}/{len(all_paragraphs)}")

                original_text = paragraph.text
                translated_text = self._translate_text_cached(original_text, target_lang)

                # إصلاح النص العربي
                if target_lang == 'ar':
                    translated_text = self._fix_arabic_text(translated_text)

                # استبدال النص مع الحفاظ على التنسيق
                self._replace_paragraph_text(paragraph, translated_text, target_lang)

            # معالجة الجداول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                original_text = paragraph.text
                                translated_text = self._translate_text_cached(original_text, target_lang)

                                if target_lang == 'ar':
                                    translated_text = self._fix_arabic_text(translated_text)

                                self._replace_paragraph_text(paragraph, translated_text, target_lang)

            # حفظ المستند
            output_buffer = io.BytesIO()
            doc.save(output_buffer)
            output_buffer.seek(0)

            return True, output_buffer.getvalue()

        except Exception as e:
            advanced_logger.log_operation("WORD_TRANSLATION", "FAILED", {"error": str(e)})
            return False, b""

    def _replace_paragraph_text(self, paragraph, new_text, target_lang):
        """استبدال نص الفقرة مع الحفاظ على التنسيق"""
        try:
            # حفظ التنسيق الأصلي
            original_runs = list(paragraph.runs)

            # مسح النص الحالي
            paragraph.clear()

            # إضافة النص الجديد
            new_run = paragraph.add_run(new_text)

            # نسخ التنسيق
            if original_runs:
                first_run = original_runs[0]
                if first_run.font.name:
                    new_run.font.name = first_run.font.name
                if first_run.font.size:
                    new_run.font.size = first_run.font.size
                new_run.bold = first_run.bold
                new_run.italic = first_run.italic
                new_run.underline = first_run.underline

            # تحديد اتجاه النص
            if target_lang == 'ar':
                from docx.enum.text import WD_ALIGN_PARAGRAPH
                from docx.oxml.ns import qn
                paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
                try:
                    paragraph._element.set(qn('w:bidi'), '1')
                except:
                    pass

        except Exception as e:
            advanced_logger.log_operation("PARAGRAPH_REPLACEMENT", "FAILED", {"error": str(e)})

    def _translate_text_cached(self, text, target_lang):
        """ترجمة النص مع التخزين المؤقت"""
        if not text or not text.strip():
            return text

        # إنشاء مفتاح للتخزين المؤقت
        cache_key = hashlib.md5(f"{text}_{target_lang}".encode()).hexdigest()

        # التحقق من التخزين المؤقت
        if cache_key in self.translation_cache:
            return self.translation_cache[cache_key]

        try:
            # ترجمة النص
            translator = GoogleTranslator(source='auto', target=target_lang)
            translated = translator.translate(text)

            # حفظ في التخزين المؤقت
            self.translation_cache[cache_key] = translated

            advanced_logger.log_operation("TEXT_TRANSLATION", "SUCCESS", {
                "original_length": len(text),
                "translated_length": len(translated),
                "cached": False
            })

            return translated

        except Exception as e:
            advanced_logger.log_operation("TEXT_TRANSLATION", "FAILED", {
                "error": str(e),
                "text": text[:50]
            })
            return text

    def _fix_arabic_text(self, text):
        """إصلاح النص العربي"""
        try:
            # إعادة تشكيل الأحرف العربية
            reshaped_text = arabic_reshaper.reshape(text)
            # إصلاح اتجاه النص
            final_text = get_display(reshaped_text)
            return final_text
        except Exception as e:
            advanced_logger.log_operation("ARABIC_TEXT_FIX", "FAILED", {"error": str(e)})
            return text

    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            advanced_logger.log_operation("CLEANUP", "SUCCESS", {"temp_dir": self.temp_dir})
        except Exception as e:
            advanced_logger.log_operation("CLEANUP", "FAILED", {"error": str(e)})

    def __del__(self):
        """تنظيف تلقائي"""
        self.cleanup()

# إنشاء مثيل عام
ultimate_translator = UltimatePDFTranslator()

def translate_pdf_ultimate(file_data: bytes, file_name: str,
                         target_lang: str = 'ar', progress_callback=None) -> Tuple[bool, bytes, str]:
    """دالة مساعدة للترجمة النهائية"""
    return ultimate_translator.translate_pdf(file_data, target_lang, progress_callback)
