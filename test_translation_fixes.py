#!/usr/bin/env python3
"""
اختبار إصلاحات نظام الترجمة الشامل
"""

import os
import sys
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_file_prompt_system():
    """اختبار نظام برومبت الملفات"""
    print("🔍 اختبار نظام برومبت الملفات...")
    
    try:
        from keyboards.builder import (
            get_translation_file_prompt, set_translation_file_prompt,
            create_translation_button
        )
        
        # إنشاء زر ترجمة للاختبار
        test_button_id = 9998
        create_translation_button(test_button_id, 'to_arabic')
        
        # اختبار تحديد برومبت الملفات
        file_prompt = "هذا برومبت مخصص لترجمة النصوص المستخرجة من الملفات (PDF, Word, إلخ)"
        set_translation_file_prompt(test_button_id, file_prompt)
        
        # اختبار جلب برومبت الملفات
        retrieved_file_prompt = get_translation_file_prompt(test_button_id)
        
        if retrieved_file_prompt == file_prompt:
            print("✅ برومبت ترجمة الملفات تم حفظه وجلبه بنجاح")
        else:
            print(f"❌ خطأ في برومبت الملفات: متوقع '{file_prompt}', تم جلب '{retrieved_file_prompt}'")
            return False
        
        # تنظيف بيانات الاختبار
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM translation_settings WHERE button_id = ?', (test_button_id,))
        cursor.execute('DELETE FROM translation_global_settings WHERE key LIKE ?', (f'%_{test_button_id}',))
        conn.commit()
        conn.close()
        
        print("✅ نظام برومبت الملفات يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار برومبت الملفات: {str(e)}")
        return False

def test_tesseract_setup():
    """اختبار إعداد tesseract المحسن"""
    print("\n🔍 اختبار إعداد tesseract...")
    
    try:
        from text_extraction import text_extractor
        
        # اختبار التحقق من توفر tesseract
        is_available = text_extractor.is_tesseract_available()
        
        if is_available:
            print("✅ tesseract متوفر ويعمل بشكل صحيح")
        else:
            print("⚠️ tesseract غير متوفر - سيتم عرض رسالة تثبيت مفيدة")
        
        # اختبار رسالة الخطأ المحسنة
        test_image_data = b"fake_image_data"
        success, message = text_extractor.extract_text_from_image(test_image_data)
        
        if not success and "tesseract" in message:
            if "تثبيت" in message or "install" in message:
                print("✅ رسالة خطأ tesseract محسنة ومفيدة")
            else:
                print("❌ رسالة خطأ tesseract غير مفيدة")
                return False
        
        print("✅ نظام tesseract محسن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار tesseract: {str(e)}")
        return False

def test_pdf_font_fixes():
    """اختبار إصلاحات خطوط PDF"""
    print("\n🔍 اختبار إصلاحات خطوط PDF...")
    
    try:
        from file_processing import file_processor
        
        # اختبار إعداد الخطوط
        file_processor.setup_arabic_fonts()
        
        if hasattr(file_processor, 'arabic_font'):
            print(f"✅ تم إعداد الخط: {file_processor.arabic_font}")
        else:
            print("❌ فشل في إعداد الخط")
            return False
        
        # اختبار تنظيف النص
        test_text = "نص تجريبي\x00\x08\u200e\ufeff مع أحرف مشكلة"
        clean_text = file_processor.clean_text_for_pdf(test_text)
        
        # التحقق من إزالة الأحرف المشكلة
        problematic_chars = ['\x00', '\x08', '\u200e', '\ufeff']
        has_problematic = any(char in clean_text for char in problematic_chars)
        
        if not has_problematic:
            print("✅ تنظيف النص من الأحرف المشكلة يعمل")
        else:
            print("❌ فشل في تنظيف النص من الأحرف المشكلة")
            return False
        
        print("✅ إصلاحات خطوط PDF تعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار خطوط PDF: {str(e)}")
        return False

def test_translation_prompt_selection():
    """اختبار اختيار البرومبت المناسب للترجمة"""
    print("\n🔍 اختبار اختيار البرومبت المناسب...")
    
    try:
        from translation_system import translation_system
        
        # اختبار وجود الدوال الجديدة
        if hasattr(translation_system, 'get_translation_file_prompt'):
            print("✅ دالة get_translation_file_prompt موجودة")
        else:
            print("❌ دالة get_translation_file_prompt مفقودة")
            return False
        
        # اختبار معاملات الدالة الجديدة
        import inspect
        sig = inspect.signature(translation_system.translate_text_ai)
        params = list(sig.parameters.keys())
        
        if 'is_file_text' in params:
            print("✅ معامل is_file_text موجود في translate_text_ai")
        else:
            print("❌ معامل is_file_text مفقود في translate_text_ai")
            return False
        
        print("✅ نظام اختيار البرومبت محسن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار البرومبت: {str(e)}")
        return False

def test_prompt_management_ui():
    """اختبار واجهة إدارة البرومبتات"""
    print("\n🔍 اختبار واجهة إدارة البرومبتات...")
    
    try:
        from telebot.types import ReplyKeyboardMarkup, KeyboardButton
        
        # محاكاة كيبورد اختيار نوع البرومبت
        prompt_type_keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        prompt_type_keyboard.add(
            KeyboardButton("📝 برومبت ترجمة النص"),
            KeyboardButton("🖼️ برومبت ترجمة الصور")
        )
        prompt_type_keyboard.add(
            KeyboardButton("📄 برومبت ترجمة الملفات")
        )
        prompt_type_keyboard.add(KeyboardButton("❌ إلغاء"))
        
        # التحقق من بنية الكيبورد
        if len(prompt_type_keyboard.keyboard) == 3:
            print("✅ كيبورد اختيار البرومبت يحتوي على 3 صفوف")
        else:
            print(f"❌ كيبورد اختيار البرومبت يحتوي على {len(prompt_type_keyboard.keyboard)} صفوف بدلاً من 3")
            return False
        
        # التحقق من وجود زر الملفات
        file_button_found = False
        for row in prompt_type_keyboard.keyboard:
            for button in row:
                if "📄" in button.text and "ملفات" in button.text:
                    file_button_found = True
                    break
        
        if file_button_found:
            print("✅ زر برومبت الملفات موجود في الكيبورد")
        else:
            print("❌ زر برومبت الملفات مفقود في الكيبورد")
            return False
        
        print("✅ واجهة إدارة البرومبتات محسنة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة البرومبتات: {str(e)}")
        return False

def test_error_handling():
    """اختبار معالجة الأخطاء المحسنة"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    try:
        from text_extraction import text_extractor
        from file_processing import file_processor
        
        # اختبار معالجة خطأ tesseract
        fake_image = b"not_an_image"
        success, error_msg = text_extractor.extract_text_from_image(fake_image)
        
        if not success:
            if "tesseract" in error_msg.lower() or "تثبيت" in error_msg:
                print("✅ رسالة خطأ tesseract واضحة ومفيدة")
            else:
                print("✅ رسالة خطأ عامة للصور")
        
        # اختبار معالجة خطأ PDF
        try:
            # محاولة إنشاء PDF مع نص مشكل
            import io
            from reportlab.pdfgen import canvas
            
            buffer = io.BytesIO()
            c = canvas.Canvas(buffer)
            
            # اختبار تنظيف النص
            problematic_text = "نص\x00مشكل\ufeff"
            clean_text = file_processor.clean_text_for_pdf(problematic_text)
            
            if len(clean_text) < len(problematic_text):
                print("✅ تنظيف النص المشكل يعمل")
            else:
                print("⚠️ تنظيف النص قد لا يعمل كما متوقع")
            
        except Exception as pdf_error:
            print("⚠️ اختبار PDF تم تخطيه بسبب مشاكل في البيئة")
        
        print("✅ معالجة الأخطاء محسنة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الأخطاء: {str(e)}")
        return False

def main():
    """تشغيل جميع اختبارات الإصلاحات"""
    print("🚀 بدء اختبار إصلاحات نظام الترجمة الشامل")
    print("=" * 70)
    
    tests = [
        ("نظام برومبت الملفات", test_file_prompt_system),
        ("إعداد tesseract المحسن", test_tesseract_setup),
        ("إصلاحات خطوط PDF", test_pdf_font_fixes),
        ("اختيار البرومبت المناسب", test_translation_prompt_selection),
        ("واجهة إدارة البرومبتات", test_prompt_management_ui),
        ("معالجة الأخطاء المحسنة", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع إصلاحات نظام الترجمة نجحت!")
        print("\n✨ الإصلاحات المطبقة:")
        print("   ✅ إضافة برومبت مخصص لترجمة الملفات")
        print("   ✅ إصلاح مشكلة tesseract مع رسائل خطأ مفيدة")
        print("   ✅ إصلاح مشكلة FontBBox في ترجمة PDF")
        print("   ✅ تحسين معالجة الأخطاء والاستقرار")
        print("   ✅ واجهة إدارة محسنة للبرومبتات الثلاثة")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
