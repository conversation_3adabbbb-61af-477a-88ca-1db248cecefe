"""
نظام استخراج النص من الصور والملفات
يدعم PDF, Word, والصور مع OCR
"""

import os
import tempfile
import pytesseract
from PIL import Image
import PyPDF2
import pdfplumber
from docx import Document
import io
import requests

class TextExtractor:
    def __init__(self):
        # إعداد مسار tesseract للـ Linux
        self.setup_tesseract()
    
    def setup_tesseract(self):
        """إعداد tesseract للعمل على أنظمة مختلفة مع تثبيت تلقائي"""
        import platform
        import shutil

        # التحقق من وجود tesseract في PATH أولاً
        tesseract_path = shutil.which('tesseract')
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            print(f"تم العثور على tesseract في: {tesseract_path}")
            return True

        # مسارات tesseract المحتملة حسب نظام التشغيل
        system = platform.system().lower()

        if system == 'windows':
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
                r'D:\Tesseract-OCR\tesseract.exe',
                r'C:\tesseract\tesseract.exe'
            ]
        elif system == 'darwin':  # macOS
            possible_paths = [
                '/usr/local/bin/tesseract',
                '/opt/homebrew/bin/tesseract',
                '/usr/bin/tesseract'
            ]
        else:  # Linux
            possible_paths = [
                '/usr/bin/tesseract',
                '/usr/local/bin/tesseract',
                '/snap/bin/tesseract'
            ]

        # البحث عن tesseract في المسارات المحتملة
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                print(f"تم العثور على tesseract في: {path}")
                return True

        # محاولة تثبيت tesseract تلقائياً
        print("محاولة تثبيت tesseract تلقائياً...")
        if self.install_tesseract_automatically():
            return True

        # إذا فشل كل شيء، استخدم إعداد افتراضي
        print("تحذير: tesseract غير متوفر، سيتم استخدام طرق بديلة")
        return False

    def install_tesseract_automatically(self):
        """تثبيت tesseract تلقائياً"""
        try:
            import platform
            import subprocess
            import os

            system = platform.system().lower()

            if system == 'windows':
                return self.install_tesseract_windows()
            elif system == 'linux':
                return self.install_tesseract_linux()
            elif system == 'darwin':
                return self.install_tesseract_macos()

            return False

        except Exception as e:
            print(f"فشل في التثبيت التلقائي: {str(e)}")
            return False

    def install_tesseract_windows(self):
        """تثبيت tesseract على Windows"""
        try:
            import subprocess
            import os

            # محاولة تثبيت عبر chocolatey إذا كان متوفراً
            try:
                subprocess.run(['choco', 'install', 'tesseract', '-y'],
                             check=True, capture_output=True, timeout=300)
                print("تم تثبيت tesseract عبر chocolatey")
                self.setup_tesseract()  # إعادة الإعداد
                return True
            except:
                pass

            # محاولة تثبيت عبر winget
            try:
                subprocess.run(['winget', 'install', 'UB-Mannheim.TesseractOCR'],
                             check=True, capture_output=True, timeout=300)
                print("تم تثبيت tesseract عبر winget")
                self.setup_tesseract()  # إعادة الإعداد
                return True
            except:
                pass

            return False

        except Exception as e:
            return False

    def install_tesseract_linux(self):
        """تثبيت tesseract على Linux"""
        try:
            import subprocess

            # محاولة تثبيت عبر apt
            try:
                subprocess.run(['sudo', 'apt', 'update'], check=True, capture_output=True)
                subprocess.run(['sudo', 'apt', 'install', '-y', 'tesseract-ocr', 'tesseract-ocr-ara'],
                             check=True, capture_output=True)
                print("تم تثبيت tesseract عبر apt")
                return True
            except:
                pass

            # محاولة تثبيت عبر yum
            try:
                subprocess.run(['sudo', 'yum', 'install', '-y', 'tesseract'],
                             check=True, capture_output=True)
                print("تم تثبيت tesseract عبر yum")
                return True
            except:
                pass

            return False

        except Exception as e:
            return False

    def install_tesseract_macos(self):
        """تثبيت tesseract على macOS"""
        try:
            import subprocess

            # محاولة تثبيت عبر brew
            subprocess.run(['brew', 'install', 'tesseract'],
                         check=True, capture_output=True)
            print("تم تثبيت tesseract عبر brew")
            return True

        except Exception as e:
            return False
    
    def extract_text_from_image(self, image_data, languages='ara+eng'):
        """استخراج النص من الصورة باستخدام OCR مع معالجة قوية للأخطاء"""
        try:
            # إعداد tesseract أولاً
            self.setup_tesseract()

            # تحويل البيانات إلى صورة
            image = Image.open(io.BytesIO(image_data))

            # تحسين الصورة للـ OCR
            image = self.preprocess_image(image)

            # محاولة استخراج النص مع خيارات متعددة
            text = self.extract_with_multiple_methods(image, languages)

            if text and text.strip():
                return True, text.strip()
            else:
                return False, "❌ لم يتم العثور على نص في هذه الصورة."

        except Exception as e:
            # محاولة استخراج بطريقة بديلة
            try:
                return self.fallback_text_extraction(image_data)
            except:
                return False, "❌ لا يمكن استخراج النص من هذه الصورة."

    def extract_with_multiple_methods(self, image, languages):
        """استخراج النص بطرق متعددة"""
        try:
            # الطريقة الأولى: tesseract عادي
            try:
                text = pytesseract.image_to_string(image, lang=languages)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الثانية: tesseract مع إعدادات محسنة
            try:
                custom_config = r'--oem 3 --psm 6'
                text = pytesseract.image_to_string(image, lang=languages, config=custom_config)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الثالثة: tesseract مع إعدادات مختلفة
            try:
                custom_config = r'--oem 1 --psm 3'
                text = pytesseract.image_to_string(image, lang=languages, config=custom_config)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الرابعة: إنجليزي فقط
            try:
                text = pytesseract.image_to_string(image, lang='eng')
                if text and text.strip():
                    return text
            except:
                pass

            return ""

        except Exception as e:
            return ""

    def fallback_text_extraction(self, image_data):
        """طريقة بديلة لاستخراج النص"""
        try:
            # محاولة مع إعدادات مبسطة
            image = Image.open(io.BytesIO(image_data))

            # تحويل إلى رمادي
            if image.mode != 'L':
                image = image.convert('L')

            # محاولة استخراج بسيط
            text = pytesseract.image_to_string(image, lang='eng')

            if text and text.strip():
                return True, text.strip()
            else:
                return False, "❌ لم يتم العثور على نص في الصورة."

        except Exception as e:
            return False, "❌ فشل في معالجة الصورة."

    def is_tesseract_available(self):
        """التحقق من توفر tesseract"""
        try:
            # محاولة تشغيل tesseract للتحقق من وجوده
            pytesseract.get_tesseract_version()
            return True
        except:
            return False

    def auto_install_tesseract(self):
        """محاولة تثبيت tesseract تلقائياً"""
        try:
            import platform
            import subprocess

            system = platform.system().lower()

            if system == 'windows':
                # تحميل وتثبيت tesseract على Windows
                return self._install_tesseract_windows()
            elif system == 'linux':
                # تثبيت tesseract على Linux
                return self._install_tesseract_linux()
            elif system == 'darwin':
                # تثبيت tesseract على macOS
                return self._install_tesseract_macos()

            return False
        except:
            return False

    def _install_tesseract_windows(self):
        """تثبيت tesseract على Windows"""
        try:
            import subprocess
            import urllib.request
            import tempfile

            # رابط تحميل tesseract للـ Windows
            download_url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3/tesseract-ocr-w64-setup-5.3.3.20231005.exe"

            # تحميل الملف
            with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as tmp_file:
                urllib.request.urlretrieve(download_url, tmp_file.name)

                # تشغيل التثبيت الصامت
                subprocess.run([tmp_file.name, '/S'], check=True)

                # إعادة إعداد tesseract
                self.setup_tesseract()
                return self.is_tesseract_available()

        except:
            return False

    def _install_tesseract_linux(self):
        """تثبيت tesseract على Linux"""
        try:
            import subprocess

            # محاولة تثبيت tesseract باستخدام apt
            subprocess.run(['sudo', 'apt', 'update'], check=True, capture_output=True)
            subprocess.run(['sudo', 'apt', 'install', '-y', 'tesseract-ocr', 'tesseract-ocr-ara'],
                         check=True, capture_output=True)

            # إعادة إعداد tesseract
            self.setup_tesseract()
            return self.is_tesseract_available()

        except:
            return False

    def _install_tesseract_macos(self):
        """تثبيت tesseract على macOS"""
        try:
            import subprocess

            # محاولة تثبيت tesseract باستخدام brew
            subprocess.run(['brew', 'install', 'tesseract'], check=True, capture_output=True)

            # إعادة إعداد tesseract
            self.setup_tesseract()
            return self.is_tesseract_available()

        except:
            return False
    
    def preprocess_image(self, image):
        """تحسين الصورة لتحسين دقة OCR"""
        try:
            # تحويل إلى RGB إذا لزم الأمر
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # تكبير الصورة لتحسين الدقة
            width, height = image.size
            if width < 1000:
                scale_factor = 1000 / width
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            return image
            
        except Exception as e:
            return image  # إرجاع الصورة الأصلية في حالة الخطأ
    
    def extract_text_from_pdf(self, file_data):
        """استخراج النص من ملف PDF"""
        try:
            # محاولة استخدام pdfplumber أولاً (أفضل للنصوص المعقدة)
            success, text = self._extract_pdf_with_pdfplumber(file_data)
            if success and text.strip():
                return True, text
            
            # إذا فشل، استخدم PyPDF2
            success, text = self._extract_pdf_with_pypdf2(file_data)
            if success:
                return True, text
            
            return False, "فشل في استخراج النص من ملف PDF"
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF: {str(e)}"
    
    def _extract_pdf_with_pdfplumber(self, file_data):
        """استخراج النص باستخدام pdfplumber"""
        try:
            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                text_parts = []
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(f"--- صفحة {page_num + 1} ---\n{page_text}\n")
                
                return True, "\n".join(text_parts)
                
        except Exception as e:
            return False, str(e)
    
    def _extract_pdf_with_pypdf2(self, file_data):
        """استخراج النص باستخدام PyPDF2"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
            text_parts = []
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(f"--- صفحة {page_num + 1} ---\n{page_text}\n")
            
            return True, "\n".join(text_parts)
            
        except Exception as e:
            return False, str(e)
    
    def extract_text_from_docx(self, file_data):
        """استخراج النص من ملف Word"""
        try:
            doc = Document(io.BytesIO(file_data))
            text_parts = []
            
            # استخراج النص من الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # استخراج النص من الجداول
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(" | ".join(row_text))
            
            return True, "\n".join(text_parts)
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف Word: {str(e)}"
    
    def extract_text_from_file(self, file_data, file_name):
        """استخراج النص من ملف حسب نوعه"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_data)
        elif file_extension in ['.docx', '.doc']:
            if file_extension == '.doc':
                return False, "ملفات .doc غير مدعومة. يرجى استخدام .docx"
            return self.extract_text_from_docx(file_data)
        elif file_extension in ['.txt']:
            try:
                text = file_data.decode('utf-8')
                return True, text
            except:
                try:
                    text = file_data.decode('cp1256')  # ترميز Windows Arabic
                    return True, text
                except:
                    return False, "فشل في قراءة ملف النص"
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            return self.extract_text_from_image(file_data)
        else:
            return False, f"نوع الملف غير مدعوم: {file_extension}"
    
    def download_telegram_file(self, bot, file_id):
        """تحميل ملف من تليجرام"""
        try:
            file_info = bot.get_file(file_id)
            file_url = f"https://api.telegram.org/file/bot{bot.token}/{file_info.file_path}"
            
            response = requests.get(file_url)
            if response.status_code == 200:
                return True, response.content
            else:
                return False, f"فشل في تحميل الملف: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في تحميل الملف: {str(e)}"
    
    def get_file_info(self, file_data, file_name):
        """الحصول على معلومات الملف"""
        file_size = len(file_data)
        file_extension = os.path.splitext(file_name.lower())[1]
        
        # تحديد نوع الملف
        file_type = "غير معروف"
        if file_extension == '.pdf':
            file_type = "PDF"
        elif file_extension in ['.docx', '.doc']:
            file_type = "Word"
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            file_type = "صورة"
        elif file_extension == '.txt':
            file_type = "نص"
        
        return {
            'size': file_size,
            'size_mb': round(file_size / (1024 * 1024), 2),
            'extension': file_extension,
            'type': file_type,
            'name': file_name
        }
    
    def is_supported_file(self, file_name):
        """التحقق من دعم نوع الملف"""
        supported_extensions = ['.pdf', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        file_extension = os.path.splitext(file_name.lower())[1]
        return file_extension in supported_extensions
    
    def check_file_size(self, file_data, max_size_mb=10):
        """التحقق من حجم الملف"""
        file_size_mb = len(file_data) / (1024 * 1024)
        return file_size_mb <= max_size_mb, file_size_mb

# إنشاء مثيل عام للاستخدام
text_extractor = TextExtractor()
