"""
نظام استخراج النص من الصور والملفات
يدعم PDF, Word, والصور مع PaddleOCR (أفضل من tesseract)
"""

import os
import tempfile
import numpy as np
from PIL import Image
import PyPDF2
import pdfplumber
from docx import Document
import io
import requests

class TextExtractor:
    def __init__(self):
        # إعداد PaddleOCR
        self.ocr = None
        self.setup_paddle_ocr()
    
    def setup_paddle_ocr(self):
        """إعداد PaddleOCR - أفضل وأسرع من tesseract"""
        try:
            # محاولة استيراد PaddleOCR
            try:
                from paddleocr import PaddleOCR

                # إنشاء كائن PaddleOCR مع دعم العربية والإنجليزية
                self.ocr = PaddleOCR(
                    use_textline_orientation=True,  # تصحيح زاوية النص (الإصدار الجديد)
                    lang='ar'  # العربية كلغة أساسية
                )

                print("✅ تم إعداد PaddleOCR بنجاح")
                return True

            except ImportError:
                print("📦 تثبيت PaddleOCR...")
                if self.install_paddle_ocr():
                    from paddleocr import PaddleOCR
                    self.ocr = PaddleOCR(
                        use_textline_orientation=True,
                        lang='ar'
                    )
                    print("✅ تم تثبيت وإعداد PaddleOCR بنجاح")
                    return True
                else:
                    print("❌ فشل في تثبيت PaddleOCR")
                    return False

        except Exception as e:
            print(f"❌ خطأ في إعداد PaddleOCR: {str(e)}")
            return False

    def install_paddle_ocr(self):
        """تثبيت PaddleOCR تلقائياً"""
        try:
            import subprocess
            import sys

            print("🔧 تثبيت PaddleOCR...")

            # تثبيت PaddleOCR
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install',
                'paddleocr', 'paddlepaddle'
            ], capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                print("✅ تم تثبيت PaddleOCR بنجاح")
                return True
            else:
                print(f"❌ فشل في تثبيت PaddleOCR: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ خطأ في تثبيت PaddleOCR: {str(e)}")
            return False

    def is_paddle_ocr_available(self):
        """فحص توفر PaddleOCR"""
        return self.ocr is not None

    def old_setup_tesseract(self):
        """إعداد tesseract للعمل على أنظمة مختلفة مع تثبيت ذكي مرة واحدة فقط"""
        import platform
        import shutil

        # فحص ملف علامة التثبيت
        install_marker_file = 'data/tesseract_installed.marker'

        # التحقق من وجود tesseract في PATH أولاً
        tesseract_path = shutil.which('tesseract')
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            # إنشاء ملف علامة التثبيت
            self.create_install_marker(install_marker_file)
            return True

        # مسارات tesseract المحتملة حسب نظام التشغيل
        system = platform.system().lower()

        if system == 'windows':
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
                r'D:\Tesseract-OCR\tesseract.exe',
                r'C:\tesseract\tesseract.exe'
            ]
        elif system == 'darwin':  # macOS
            possible_paths = [
                '/usr/local/bin/tesseract',
                '/opt/homebrew/bin/tesseract',
                '/usr/bin/tesseract'
            ]
        else:  # Linux
            possible_paths = [
                '/usr/bin/tesseract',
                '/usr/local/bin/tesseract',
                '/snap/bin/tesseract'
            ]

        # البحث عن tesseract في المسارات المحتملة
        for path in possible_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                # إنشاء ملف علامة التثبيت
                self.create_install_marker(install_marker_file)
                return True

        # فحص ما إذا كان التثبيت تم من قبل
        if os.path.exists(install_marker_file):
            print("tesseract تم تثبيته من قبل، لكن قد يحتاج إعادة تشغيل النظام")
            return False

        # محاولة تثبيت tesseract تلقائياً (مرة واحدة فقط)
        print(f"تثبيت tesseract لأول مرة على نظام {system}...")
        if self.install_tesseract_automatically():
            # إنشاء ملف علامة التثبيت
            self.create_install_marker(install_marker_file)
            return True

        # إذا فشل كل شيء، استخدم إعداد افتراضي
        print("تحذير: tesseract غير متوفر، سيتم استخدام طرق بديلة")
        return False

    def create_install_marker(self, marker_file):
        """إنشاء ملف علامة التثبيت"""
        try:
            import datetime
            os.makedirs(os.path.dirname(marker_file), exist_ok=True)
            with open(marker_file, 'w', encoding='utf-8') as f:
                f.write(f"tesseract installed on {datetime.datetime.now()}\n")
                f.write(f"system: {platform.system()}\n")
                f.write(f"tesseract_path: {pytesseract.pytesseract.tesseract_cmd}\n")
        except:
            pass  # تجاهل الأخطاء في إنشاء الملف

    def install_tesseract_automatically(self):
        """تثبيت tesseract تلقائياً حسب نظام التشغيل"""
        try:
            import platform
            import subprocess
            import os

            system = platform.system().lower()
            print(f"🔧 بدء تثبيت tesseract على نظام {system}...")

            if system == 'windows':
                success = self.install_tesseract_windows()
                if success:
                    print("✅ تم تثبيت tesseract على Windows بنجاح!")
                return success

            elif system == 'linux':
                success = self.install_tesseract_linux()
                if success:
                    print("✅ تم تثبيت tesseract على Linux بنجاح!")
                return success

            elif system == 'darwin':
                success = self.install_tesseract_macos()
                if success:
                    print("✅ تم تثبيت tesseract على macOS بنجاح!")
                return success

            print(f"❌ نظام التشغيل {system} غير مدعوم للتثبيت التلقائي")
            return False

        except Exception as e:
            print(f"❌ فشل في التثبيت التلقائي: {str(e)}")
            return False

    def install_tesseract_windows(self):
        """تثبيت tesseract على Windows بطرق متعددة"""
        try:
            import subprocess
            import os

            print("🔧 محاولة تثبيت tesseract على Windows...")

            # الطريقة الأولى: winget (الأفضل والأحدث)
            try:
                print("   📦 محاولة التثبيت عبر winget...")
                result = subprocess.run(['winget', 'install', 'UB-Mannheim.TesseractOCR', '--accept-source-agreements', '--accept-package-agreements'],
                                     check=True, capture_output=True, timeout=300, text=True)
                print("   ✅ تم تثبيت tesseract عبر winget")

                # إعادة فحص المسارات
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر winget")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر winget: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ winget غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في winget: {str(e)}")

            # الطريقة الثانية: chocolatey
            try:
                print("   🍫 محاولة التثبيت عبر chocolatey...")
                result = subprocess.run(['choco', 'install', 'tesseract', '-y'],
                                     check=True, capture_output=True, timeout=300, text=True)
                print("   ✅ تم تثبيت tesseract عبر chocolatey")

                # إعادة فحص المسارات
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر chocolatey")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر chocolatey: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ chocolatey غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في chocolatey: {str(e)}")

            print("   ❌ فشل في جميع طرق التثبيت على Windows")
            return False

        except Exception as e:
            print(f"   ❌ خطأ عام في تثبيت Windows: {str(e)}")
            return False

    def verify_tesseract_installation(self):
        """التحقق من نجاح تثبيت tesseract"""
        try:
            import shutil

            # فحص PATH
            tesseract_path = shutil.which('tesseract')
            if tesseract_path:
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
                print(f"   ✅ تم العثور على tesseract في: {tesseract_path}")
                return True

            # فحص المسارات الشائعة
            import platform
            system = platform.system().lower()

            if system == 'windows':
                common_paths = [
                    r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                    r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe'
                ]
            else:
                common_paths = [
                    '/usr/bin/tesseract',
                    '/usr/local/bin/tesseract'
                ]

            for path in common_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"   ✅ تم العثور على tesseract في: {path}")
                    return True

            return False

        except Exception as e:
            print(f"   ❌ خطأ في التحقق من التثبيت: {str(e)}")
            return False

    def install_tesseract_linux(self):
        """تثبيت tesseract على Linux بطرق متعددة"""
        try:
            import subprocess

            print("🐧 محاولة تثبيت tesseract على Linux...")

            # الطريقة الأولى: apt (Ubuntu/Debian)
            try:
                print("   📦 محاولة التثبيت عبر apt...")

                # تحديث قائمة الحزم
                subprocess.run(['sudo', 'apt', 'update'],
                             check=True, capture_output=True, timeout=120)

                # تثبيت tesseract مع دعم العربية
                subprocess.run(['sudo', 'apt', 'install', '-y', 'tesseract-ocr', 'tesseract-ocr-ara', 'tesseract-ocr-eng'],
                             check=True, capture_output=True, timeout=300)

                print("   ✅ تم تثبيت tesseract عبر apt")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر apt")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر apt: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ apt غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في apt: {str(e)}")

            # الطريقة الثانية: yum (RedHat/CentOS/Fedora)
            try:
                print("   📦 محاولة التثبيت عبر yum...")
                subprocess.run(['sudo', 'yum', 'install', '-y', 'tesseract', 'tesseract-langpack-ara'],
                             check=True, capture_output=True, timeout=300)
                print("   ✅ تم تثبيت tesseract عبر yum")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر yum")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر yum: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ yum غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في yum: {str(e)}")

            # الطريقة الثالثة: dnf (Fedora الحديثة)
            try:
                print("   📦 محاولة التثبيت عبر dnf...")
                subprocess.run(['sudo', 'dnf', 'install', '-y', 'tesseract', 'tesseract-langpack-ara'],
                             check=True, capture_output=True, timeout=300)
                print("   ✅ تم تثبيت tesseract عبر dnf")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر dnf")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر dnf: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ dnf غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في dnf: {str(e)}")

            # الطريقة الرابعة: snap
            try:
                print("   📦 محاولة التثبيت عبر snap...")
                subprocess.run(['sudo', 'snap', 'install', 'tesseract'],
                             check=True, capture_output=True, timeout=300)
                print("   ✅ تم تثبيت tesseract عبر snap")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر snap")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر snap: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ snap غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في snap: {str(e)}")

            print("   ❌ فشل في جميع طرق التثبيت على Linux")
            return False

        except Exception as e:
            print(f"   ❌ خطأ عام في تثبيت Linux: {str(e)}")
            return False

    def install_tesseract_macos(self):
        """تثبيت tesseract على macOS"""
        try:
            import subprocess

            print("🍎 محاولة تثبيت tesseract على macOS...")

            # الطريقة الأولى: Homebrew
            try:
                print("   🍺 محاولة التثبيت عبر Homebrew...")

                # تحديث brew أولاً
                subprocess.run(['brew', 'update'],
                             check=True, capture_output=True, timeout=120)

                # تثبيت tesseract
                subprocess.run(['brew', 'install', 'tesseract'],
                             check=True, capture_output=True, timeout=300)

                print("   ✅ تم تثبيت tesseract عبر Homebrew")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر Homebrew")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر Homebrew: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ Homebrew غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في Homebrew: {str(e)}")

            # الطريقة الثانية: MacPorts
            try:
                print("   🚢 محاولة التثبيت عبر MacPorts...")
                subprocess.run(['sudo', 'port', 'install', 'tesseract'],
                             check=True, capture_output=True, timeout=300)
                print("   ✅ تم تثبيت tesseract عبر MacPorts")

                # التحقق من التثبيت
                if self.verify_tesseract_installation():
                    return True

            except subprocess.TimeoutExpired:
                print("   ⏰ انتهت مهلة التثبيت عبر MacPorts")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ فشل التثبيت عبر MacPorts: {e.returncode}")
            except FileNotFoundError:
                print("   ❌ MacPorts غير متوفر")
            except Exception as e:
                print(f"   ❌ خطأ في MacPorts: {str(e)}")

            print("   ❌ فشل في جميع طرق التثبيت على macOS")
            return False

        except Exception as e:
            print(f"   ❌ خطأ عام في تثبيت macOS: {str(e)}")
            return False
    
    def extract_text_from_image(self, image_data, languages='ar'):
        """استخراج النص من الصورة باستخدام PaddleOCR - أفضل وأسرع من tesseract"""
        try:
            # التأكد من إعداد PaddleOCR
            if not self.is_paddle_ocr_available():
                if not self.setup_paddle_ocr():
                    return False, "❌ لا يمكن تشغيل نظام استخراج النص."

            # تحويل البيانات إلى صورة
            image = Image.open(io.BytesIO(image_data))

            # تحسين الصورة للـ OCR
            image = self.preprocess_image_for_paddle(image)

            # تحويل الصورة إلى numpy array
            image_array = np.array(image)

            # استخراج النص باستخدام PaddleOCR
            results = self.ocr.ocr(image_array, cls=True)

            # معالجة النتائج
            extracted_text = self.process_paddle_results(results)

            if extracted_text and extracted_text.strip():
                return True, extracted_text.strip()
            else:
                return False, "❌ لم يتم العثور على نص في هذه الصورة."

        except Exception as e:
            # محاولة استخراج بطريقة بديلة
            try:
                return self.fallback_paddle_extraction(image_data)
            except:
                return False, f"❌ لا يمكن استخراج النص من هذه الصورة."

    def preprocess_image_for_paddle(self, image):
        """تحسين الصورة لـ PaddleOCR"""
        try:
            # تحويل إلى RGB إذا لزم الأمر
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # تحسين الجودة
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # تحسين الوضوح
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)

            return image

        except Exception as e:
            # إرجاع الصورة الأصلية في حالة الخطأ
            return image

    def process_paddle_results(self, results):
        """معالجة نتائج PaddleOCR وتحويلها إلى نص"""
        try:
            extracted_lines = []

            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        # line[1] يحتوي على النص والثقة
                        text_info = line[1]
                        if isinstance(text_info, tuple) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]

                            # قبول النص إذا كانت الثقة أكبر من 0.5
                            if confidence > 0.5 and text.strip():
                                extracted_lines.append(text.strip())
                        elif isinstance(text_info, str):
                            # في بعض الإصدارات يكون النص مباشرة
                            if text_info.strip():
                                extracted_lines.append(text_info.strip())

            # دمج الأسطر
            return '\n'.join(extracted_lines)

        except Exception as e:
            print(f"خطأ في معالجة نتائج PaddleOCR: {str(e)}")
            return ""

    def fallback_paddle_extraction(self, image_data):
        """طريقة بديلة لاستخراج النص مع PaddleOCR"""
        try:
            # محاولة مع إعدادات مبسطة
            image = Image.open(io.BytesIO(image_data))

            # تحويل إلى رمادي
            if image.mode != 'L':
                image = image.convert('L')

            # تحويل مرة أخرى إلى RGB لـ PaddleOCR
            image = image.convert('RGB')

            # تحويل إلى numpy array
            image_array = np.array(image)

            # محاولة استخراج مع إعدادات مختلفة
            if self.ocr:
                results = self.ocr.ocr(image_array, cls=False)
                text = self.process_paddle_results(results)

                if text and text.strip():
                    return True, text.strip()

            return False, "❌ لم يتم العثور على نص في الصورة."

        except Exception as e:
            return False, f"❌ فشل في معالجة الصورة: {str(e)}"

    def extract_with_multiple_methods(self, image, languages):
        """استخراج النص بطرق متعددة"""
        try:
            # الطريقة الأولى: tesseract عادي
            try:
                text = pytesseract.image_to_string(image, lang=languages)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الثانية: tesseract مع إعدادات محسنة
            try:
                custom_config = r'--oem 3 --psm 6'
                text = pytesseract.image_to_string(image, lang=languages, config=custom_config)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الثالثة: tesseract مع إعدادات مختلفة
            try:
                custom_config = r'--oem 1 --psm 3'
                text = pytesseract.image_to_string(image, lang=languages, config=custom_config)
                if text and text.strip():
                    return text
            except:
                pass

            # الطريقة الرابعة: إنجليزي فقط
            try:
                text = pytesseract.image_to_string(image, lang='eng')
                if text and text.strip():
                    return text
            except:
                pass

            return ""

        except Exception as e:
            return ""

    def fallback_text_extraction(self, image_data):
        """طريقة بديلة لاستخراج النص"""
        try:
            # محاولة مع إعدادات مبسطة
            image = Image.open(io.BytesIO(image_data))

            # تحويل إلى رمادي
            if image.mode != 'L':
                image = image.convert('L')

            # محاولة استخراج بسيط
            text = pytesseract.image_to_string(image, lang='eng')

            if text and text.strip():
                return True, text.strip()
            else:
                return False, "❌ لم يتم العثور على نص في الصورة."

        except Exception as e:
            return False, "❌ فشل في معالجة الصورة."

    def is_tesseract_available(self):
        """التحقق من توفر tesseract"""
        try:
            # محاولة تشغيل tesseract للتحقق من وجوده
            pytesseract.get_tesseract_version()
            return True
        except:
            return False

    def auto_install_tesseract(self):
        """محاولة تثبيت tesseract تلقائياً"""
        try:
            import platform
            import subprocess

            system = platform.system().lower()

            if system == 'windows':
                # تحميل وتثبيت tesseract على Windows
                return self._install_tesseract_windows()
            elif system == 'linux':
                # تثبيت tesseract على Linux
                return self._install_tesseract_linux()
            elif system == 'darwin':
                # تثبيت tesseract على macOS
                return self._install_tesseract_macos()

            return False
        except:
            return False

    def _install_tesseract_windows(self):
        """تثبيت tesseract على Windows"""
        try:
            import subprocess
            import urllib.request
            import tempfile

            # رابط تحميل tesseract للـ Windows
            download_url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.3.3/tesseract-ocr-w64-setup-5.3.3.20231005.exe"

            # تحميل الملف
            with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as tmp_file:
                urllib.request.urlretrieve(download_url, tmp_file.name)

                # تشغيل التثبيت الصامت
                subprocess.run([tmp_file.name, '/S'], check=True)

                # إعادة إعداد tesseract
                self.setup_tesseract()
                return self.is_tesseract_available()

        except:
            return False

    def _install_tesseract_linux(self):
        """تثبيت tesseract على Linux"""
        try:
            import subprocess

            # محاولة تثبيت tesseract باستخدام apt
            subprocess.run(['sudo', 'apt', 'update'], check=True, capture_output=True)
            subprocess.run(['sudo', 'apt', 'install', '-y', 'tesseract-ocr', 'tesseract-ocr-ara'],
                         check=True, capture_output=True)

            # إعادة إعداد tesseract
            self.setup_tesseract()
            return self.is_tesseract_available()

        except:
            return False

    def _install_tesseract_macos(self):
        """تثبيت tesseract على macOS"""
        try:
            import subprocess

            # محاولة تثبيت tesseract باستخدام brew
            subprocess.run(['brew', 'install', 'tesseract'], check=True, capture_output=True)

            # إعادة إعداد tesseract
            self.setup_tesseract()
            return self.is_tesseract_available()

        except:
            return False
    
    def preprocess_image(self, image):
        """تحسين الصورة لتحسين دقة OCR"""
        try:
            # تحويل إلى RGB إذا لزم الأمر
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # تكبير الصورة لتحسين الدقة
            width, height = image.size
            if width < 1000:
                scale_factor = 1000 / width
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            return image
            
        except Exception as e:
            return image  # إرجاع الصورة الأصلية في حالة الخطأ
    
    def extract_text_from_pdf(self, file_data):
        """استخراج النص من ملف PDF"""
        try:
            # محاولة استخدام pdfplumber أولاً (أفضل للنصوص المعقدة)
            success, text = self._extract_pdf_with_pdfplumber(file_data)
            if success and text.strip():
                return True, text
            
            # إذا فشل، استخدم PyPDF2
            success, text = self._extract_pdf_with_pypdf2(file_data)
            if success:
                return True, text
            
            return False, "فشل في استخراج النص من ملف PDF"
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف PDF: {str(e)}"
    
    def _extract_pdf_with_pdfplumber(self, file_data):
        """استخراج النص باستخدام pdfplumber"""
        try:
            with pdfplumber.open(io.BytesIO(file_data)) as pdf:
                text_parts = []
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(f"--- صفحة {page_num + 1} ---\n{page_text}\n")
                
                return True, "\n".join(text_parts)
                
        except Exception as e:
            return False, str(e)
    
    def _extract_pdf_with_pypdf2(self, file_data):
        """استخراج النص باستخدام PyPDF2"""
        try:
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_data))
            text_parts = []
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                if page_text:
                    text_parts.append(f"--- صفحة {page_num + 1} ---\n{page_text}\n")
            
            return True, "\n".join(text_parts)
            
        except Exception as e:
            return False, str(e)
    
    def extract_text_from_docx(self, file_data):
        """استخراج النص من ملف Word"""
        try:
            doc = Document(io.BytesIO(file_data))
            text_parts = []
            
            # استخراج النص من الفقرات
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # استخراج النص من الجداول
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(" | ".join(row_text))
            
            return True, "\n".join(text_parts)
            
        except Exception as e:
            return False, f"خطأ في معالجة ملف Word: {str(e)}"
    
    def extract_text_from_file(self, file_data, file_name):
        """استخراج النص من ملف حسب نوعه"""
        file_extension = os.path.splitext(file_name.lower())[1]
        
        if file_extension == '.pdf':
            return self.extract_text_from_pdf(file_data)
        elif file_extension in ['.docx', '.doc']:
            if file_extension == '.doc':
                return False, "ملفات .doc غير مدعومة. يرجى استخدام .docx"
            return self.extract_text_from_docx(file_data)
        elif file_extension in ['.txt']:
            try:
                text = file_data.decode('utf-8')
                return True, text
            except:
                try:
                    text = file_data.decode('cp1256')  # ترميز Windows Arabic
                    return True, text
                except:
                    return False, "فشل في قراءة ملف النص"
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            return self.extract_text_from_image(file_data)
        else:
            return False, f"نوع الملف غير مدعوم: {file_extension}"
    
    def download_telegram_file(self, bot, file_id):
        """تحميل ملف من تليجرام"""
        try:
            file_info = bot.get_file(file_id)
            file_url = f"https://api.telegram.org/file/bot{bot.token}/{file_info.file_path}"
            
            response = requests.get(file_url)
            if response.status_code == 200:
                return True, response.content
            else:
                return False, f"فشل في تحميل الملف: {response.status_code}"
                
        except Exception as e:
            return False, f"خطأ في تحميل الملف: {str(e)}"
    
    def get_file_info(self, file_data, file_name):
        """الحصول على معلومات الملف"""
        file_size = len(file_data)
        file_extension = os.path.splitext(file_name.lower())[1]
        
        # تحديد نوع الملف
        file_type = "غير معروف"
        if file_extension == '.pdf':
            file_type = "PDF"
        elif file_extension in ['.docx', '.doc']:
            file_type = "Word"
        elif file_extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            file_type = "صورة"
        elif file_extension == '.txt':
            file_type = "نص"
        
        return {
            'size': file_size,
            'size_mb': round(file_size / (1024 * 1024), 2),
            'extension': file_extension,
            'type': file_type,
            'name': file_name
        }
    
    def is_supported_file(self, file_name):
        """التحقق من دعم نوع الملف"""
        supported_extensions = ['.pdf', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        file_extension = os.path.splitext(file_name.lower())[1]
        return file_extension in supported_extensions
    
    def check_file_size(self, file_data, max_size_mb=10):
        """التحقق من حجم الملف"""
        file_size_mb = len(file_data) / (1024 * 1024)
        return file_size_mb <= max_size_mb, file_size_mb

# إنشاء مثيل عام للاستخدام
text_extractor = TextExtractor()
